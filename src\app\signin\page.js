"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { createBrowserClient } from "@supabase/ssr";

export default function SignIn() {
  const [showPassword, setShowPassword] = useState(false);
  const [errorMsg, setErrorMsg] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();

  // Create Supabase client
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  );

  useEffect(() => {
    // Handle URL parameters for errors and messages
    const error = searchParams.get("error");
    const message = searchParams.get("message");

    if (error === "access_denied") {
      setErrorMsg(message || "Access denied. Admin privileges required.");
    } else if (error === "auth_error") {
      setErrorMsg("Authentication error. Please try signing in again.");
    }
  }, [searchParams]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setErrorMsg(""); // Clear previous error
    setIsLoading(true);

    const email = e.target.email.value;
    const password = e.target.password.value;

    console.log("Starting signin process for:", email);

    try {
      // Sign in with Supabase
      console.log("Attempting to sign in with Supabase...");
      const { data: authData, error: authError } =
        await supabase.auth.signInWithPassword({
          email,
          password,
        });

      console.log("Auth response:", { authData, authError });

      if (authError) {
        console.error("Auth error:", authError);
        setErrorMsg("Login failed: " + authError.message);
        return;
      }

      if (!authData.user) {
        console.error("No user in auth data");
        setErrorMsg("Failed to authenticate user.");
        return;
      }

      // Check role from profiles table
      console.log("Checking profile for user ID:", authData.user.id);
      const { data: profile, error: roleError } = await supabase
        .from("profiles")
        .select("role")
        .eq("user_id", authData.user.id)
        .single();

      console.log("Profile query result:", { profile, roleError });

      if (roleError) {
        console.error("Role error:", roleError);
        await supabase.auth.signOut();
        setErrorMsg(`Profile error: ${roleError.message}`);
        return;
      }

      if (!profile) {
        console.error("No profile found for user");
        await supabase.auth.signOut();
        setErrorMsg("No profile found. Please contact administrator.");
        return;
      }

      console.log("Profile role:", profile.role, "Expected: admin");
      if (profile.role !== "admin") {
        console.error("Role mismatch:", profile.role, "vs admin");
        await supabase.auth.signOut();
        setErrorMsg(`Access denied: Role '${profile.role}' is not admin.`);
        return;
      }

      // Get redirect URL from search params or default to admin
      const redirectTo = searchParams.get("redirectTo") || "/admin";

      // Success, redirect to admin or intended page
      console.log("Signin successful, redirecting to:", redirectTo);
      router.push(redirectTo);
      router.refresh(); // Refresh to update middleware state
    } catch (error) {
      console.error("Sign in error:", error);
      setErrorMsg("An unexpected error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-tr from-blue-100 to-purple-200">
      <div className="w-full max-w-md p-8 bg-white rounded-2xl shadow-xl">
        <h2 className="text-3xl font-bold text-center text-gray-800 mb-6">
          Sign In
        </h2>

        {errorMsg && (
          <div className="mb-4 text-sm text-red-600 text-center">
            {errorMsg}
          </div>
        )}

        <form className="space-y-5" onSubmit={handleSubmit}>
          <div>
            <label
              htmlFor="email"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Email
            </label>
            <input
              id="email"
              type="email"
              required
              className="w-full px-4 py-2 border border-gray-300 rounded-xl shadow-sm focus:ring-blue-500 focus:border-blue-500"
              placeholder="<EMAIL>"
            />
          </div>
          <div>
            <label
              htmlFor="password"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Password
            </label>
            <div className="relative">
              <input
                id="password"
                type={showPassword ? "text" : "password"}
                required
                className="w-full px-4 py-2 border border-gray-300 rounded-xl shadow-sm focus:ring-blue-500 focus:border-blue-500"
                placeholder="••••••••"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 right-3 flex items-center text-sm text-gray-500"
              >
                {showPassword ? "Hide" : "Show"}
              </button>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <label className="flex items-center text-sm">
              <input type="checkbox" className="mr-2" />
              Remember me
            </label>
            <a href="#" className="text-sm text-blue-500 hover:underline">
              Forgot password?
            </a>
          </div>

          <button
            type="submit"
            disabled={isLoading}
            className="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed text-white font-semibold rounded-xl shadow-md transition-colors"
          >
            {isLoading ? "Signing In..." : "Sign In"}
          </button>
        </form>
      </div>
    </div>
  );
}
