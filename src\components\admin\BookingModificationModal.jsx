"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { useToast } from '@/components/ui/Toast';
import { X, Save, Trash2, Calendar, Users, Bed } from 'lucide-react';

export default function BookingModificationModal({ 
  booking, 
  isOpen, 
  onClose, 
  onUpdate, 
  onCancel 
}) {
  const [formData, setFormData] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);
  const [cancelReason, setCancelReason] = useState('');
  const { toast } = useToast();

  useEffect(() => {
    if (booking) {
      setFormData({
        check_in_date: booking.check_in_date?.split('T')[0] || '',
        check_out_date: booking.check_out_date?.split('T')[0] || '',
        adults: booking.adults || 1,
        children: booking.children || 0,
        rooms: booking.rooms || 1,
        special_requests: booking.special_requests || '',
        total_amount: booking.total_amount || 0,
        currency: booking.currency || 'DZD',
        admin_notes: booking.admin_notes || ''
      });
    }
  }, [booking]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/bookings/${booking.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update booking');
      }

      const data = await response.json();
      toast.success('Booking updated successfully!');
      onUpdate(data.data);
      onClose();
    } catch (error) {
      console.error('Error updating booking:', error);
      toast.error(`Error updating booking: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = async () => {
    if (!cancelReason.trim()) {
      toast.error('Please provide a reason for cancellation');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/bookings/${booking.id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reason: cancelReason })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to cancel booking');
      }

      const data = await response.json();
      toast.success('Booking cancelled successfully!');
      onCancel(data.data);
      onClose();
    } catch (error) {
      console.error('Error cancelling booking:', error);
      toast.error(`Error cancelling booking: ${error.message}`);
    } finally {
      setIsLoading(false);
      setShowCancelConfirm(false);
      setCancelReason('');
    }
  };

  if (!isOpen || !booking) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            Modify Booking - {booking.booking_reference}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Customer Info (Read-only) */}
          <Card>
            <CardContent className="p-4">
              <h3 className="font-semibold text-gray-900 mb-3">Customer Information</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Name:</span> {booking.customers?.first_name} {booking.customers?.last_name}
                </div>
                <div>
                  <span className="font-medium">Email:</span> {booking.customers?.email}
                </div>
                <div>
                  <span className="font-medium">Phone:</span> {booking.customers?.phone || 'N/A'}
                </div>
                <div>
                  <span className="font-medium">Hotel:</span> {booking.hotels?.name}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Booking Details (Editable) */}
          <Card>
            <CardContent className="p-4">
              <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
                <Calendar className="w-4 h-4 mr-2" />
                Booking Details
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <Input
                  label="Check-in Date"
                  type="date"
                  value={formData.check_in_date}
                  onChange={(e) => handleInputChange('check_in_date', e.target.value)}
                />
                <Input
                  label="Check-out Date"
                  type="date"
                  value={formData.check_out_date}
                  onChange={(e) => handleInputChange('check_out_date', e.target.value)}
                />
                <Input
                  label="Adults"
                  type="number"
                  min="1"
                  value={formData.adults}
                  onChange={(e) => handleInputChange('adults', parseInt(e.target.value))}
                  icon={<Users className="w-4 h-4" />}
                />
                <Input
                  label="Children"
                  type="number"
                  min="0"
                  value={formData.children}
                  onChange={(e) => handleInputChange('children', parseInt(e.target.value))}
                  icon={<Users className="w-4 h-4" />}
                />
                <Input
                  label="Rooms"
                  type="number"
                  min="1"
                  value={formData.rooms}
                  onChange={(e) => handleInputChange('rooms', parseInt(e.target.value))}
                  icon={<Bed className="w-4 h-4" />}
                />
                <div className="flex gap-2">
                  <Input
                    label="Total Amount"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.total_amount}
                    onChange={(e) => handleInputChange('total_amount', parseFloat(e.target.value))}
                    className="flex-1"
                  />
                  <select
                    value={formData.currency}
                    onChange={(e) => handleInputChange('currency', e.target.value)}
                    className="mt-6 p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="DZD">DZD</option>
                    <option value="EUR">EUR</option>
                    <option value="USD">USD</option>
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Special Requests & Notes */}
          <Card>
            <CardContent className="p-4">
              <h3 className="font-semibold text-gray-900 mb-3">Additional Information</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Special Requests
                  </label>
                  <textarea
                    value={formData.special_requests}
                    onChange={(e) => handleInputChange('special_requests', e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    rows={3}
                    placeholder="Any special requests from the customer..."
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Admin Notes
                  </label>
                  <textarea
                    value={formData.admin_notes}
                    onChange={(e) => handleInputChange('admin_notes', e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    rows={3}
                    placeholder="Internal notes about this booking..."
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Cancel Booking Section */}
          {!showCancelConfirm ? (
            <Card className="border-red-200">
              <CardContent className="p-4">
                <h3 className="font-semibold text-red-900 mb-3">Danger Zone</h3>
                <Button
                  variant="outline"
                  className="text-red-600 border-red-300 hover:bg-red-50"
                  icon={<Trash2 className="w-4 h-4" />}
                  onClick={() => setShowCancelConfirm(true)}
                >
                  Cancel Booking
                </Button>
              </CardContent>
            </Card>
          ) : (
            <Card className="border-red-200 bg-red-50">
              <CardContent className="p-4">
                <h3 className="font-semibold text-red-900 mb-3">Confirm Cancellation</h3>
                <div className="space-y-3">
                  <textarea
                    value={cancelReason}
                    onChange={(e) => setCancelReason(e.target.value)}
                    className="w-full p-3 border border-red-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    rows={3}
                    placeholder="Please provide a reason for cancelling this booking..."
                  />
                  <div className="flex gap-3">
                    <Button
                      variant="primary"
                      className="bg-red-600 hover:bg-red-700"
                      onClick={handleCancel}
                      disabled={isLoading || !cancelReason.trim()}
                    >
                      {isLoading ? 'Cancelling...' : 'Confirm Cancellation'}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setShowCancelConfirm(false);
                        setCancelReason('');
                      }}
                    >
                      Keep Booking
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Footer Actions */}
        <div className="flex items-center justify-end gap-3 p-6 border-t bg-gray-50">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            icon={<Save className="w-4 h-4" />}
            onClick={handleSave}
            disabled={isLoading}
          >
            {isLoading ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>
    </div>
  );
}
