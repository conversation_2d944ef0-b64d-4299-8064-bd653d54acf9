"use client";
import { Card, CardContent } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import { CheckCircle, Calendar, Users, MapPin, Phone, Mail, CreditCard, Download } from "lucide-react";

export default function BookingConfirmation({ bookingData, onNewBooking }) {
  const { booking, customer, isNewCustomer } = bookingData;

  const handlePrint = () => {
    window.print();
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const calculateNights = () => {
    const checkIn = new Date(booking.check_in_date);
    const checkOut = new Date(booking.check_out_date);
    return Math.ceil((checkOut - checkIn) / (1000 * 60 * 60 * 24));
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <Card>
        <CardContent className="p-8">
          {/* Success Header */}
          <div className="text-center mb-8">
            <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Réservation Confirmée !
            </h1>
            <p className="text-lg text-gray-600">
              Votre réservation a été enregistrée avec succès
            </p>
            <div className="mt-4 p-4 bg-green-50 rounded-lg">
              <p className="text-green-800 font-semibold">
                Numéro de réservation: <span className="text-2xl">{booking.booking_reference}</span>
              </p>
              <p className="text-green-700 text-sm mt-1">
                Conservez ce numéro pour vos références
              </p>
            </div>
          </div>

          {/* Booking Details */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            {/* Hotel Information */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <MapPin className="w-5 h-5" />
                Détails de l'hôtel
              </h2>
              <div className="space-y-3">
                <div>
                  <p className="font-semibold text-lg">{booking.hotels?.name}</p>
                  <p className="text-gray-600">
                    {booking.hotels?.cities?.name}, {booking.hotels?.cities?.countries?.name}
                  </p>
                  <div className="flex items-center gap-1 mt-1">
                    {[...Array(booking.hotels?.stars || 0)].map((_, i) => (
                      <span key={i} className="text-yellow-400">★</span>
                    ))}
                    <span className="text-gray-500 ml-1">({booking.hotels?.stars} étoiles)</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Customer Information */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Users className="w-5 h-5" />
                Informations du client
              </h2>
              <div className="space-y-2">
                <p className="font-semibold">
                  {customer.first_name} {customer.last_name}
                </p>
                <div className="flex items-center gap-2 text-gray-600">
                  <Mail className="w-4 h-4" />
                  <span>{customer.email}</span>
                </div>
                {customer.phone && (
                  <div className="flex items-center gap-2 text-gray-600">
                    <Phone className="w-4 h-4" />
                    <span>{customer.phone}</span>
                  </div>
                )}
                {isNewCustomer && (
                  <p className="text-sm text-blue-600 bg-blue-50 p-2 rounded">
                    ✨ Bienvenue ! C'est votre première réservation avec nous.
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Stay Details */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              Détails du séjour
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600">Arrivée</p>
                <p className="font-semibold">{formatDate(booking.check_in_date)}</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600">Départ</p>
                <p className="font-semibold">{formatDate(booking.check_out_date)}</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600">Durée</p>
                <p className="font-semibold">{calculateNights()} nuit(s)</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600">Voyageurs</p>
                <p className="font-semibold">
                  {booking.adults} adulte(s)
                  {booking.children > 0 && `, ${booking.children} enfant(s)`}
                </p>
              </div>
            </div>
            
            <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600">Chambres</p>
                <p className="font-semibold">{booking.rooms} chambre(s)</p>
              </div>
              {booking.special_requests && (
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-600">Demandes spéciales</p>
                  <p className="font-semibold">{booking.special_requests}</p>
                </div>
              )}
            </div>
          </div>

          {/* Payment Summary */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <CreditCard className="w-5 h-5" />
              Résumé financier
            </h2>
            <div className="bg-gray-50 p-6 rounded-lg">
              <div className="flex justify-between items-center text-lg">
                <span className="font-semibold">Montant total:</span>
                <span className="font-bold text-primary text-2xl">
                  {booking.total_amount?.toLocaleString()} {booking.currency}
                </span>
              </div>
              <div className="mt-2 flex justify-between items-center text-sm text-gray-600">
                <span>Statut du paiement:</span>
                <span className={`px-3 py-1 rounded-full text-sm ${
                  booking.payment_status === 'paid' 
                    ? 'bg-green-100 text-green-800'
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {booking.payment_status === 'paid' ? 'Payé' : 'En attente'}
                </span>
              </div>
            </div>
          </div>

          {/* Next Steps */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Prochaines étapes
            </h2>
            <div className="bg-blue-50 p-6 rounded-lg">
              <ul className="space-y-2 text-blue-800">
                <li>• Un email de confirmation vous sera envoyé sous peu</li>
                <li>• Notre équipe vous contactera pour finaliser les détails</li>
                <li>• Conservez votre numéro de réservation: <strong>{booking.booking_reference}</strong></li>
                <li>• Pour toute question, contactez-nous avec votre numéro de réservation</li>
              </ul>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              variant="outline"
              onClick={handlePrint}
              icon={<Download className="w-4 h-4" />}
            >
              Imprimer la confirmation
            </Button>
            <Button
              variant="primary"
              onClick={onNewBooking}
            >
              Nouvelle réservation
            </Button>
          </div>

          {/* Contact Information */}
          <div className="mt-8 pt-8 border-t border-gray-200 text-center text-gray-600">
            <p className="mb-2">
              <strong>Bouguerrouche Travel</strong>
            </p>
            <p>Pour toute assistance: <EMAIL> | +213 123 456 789</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
