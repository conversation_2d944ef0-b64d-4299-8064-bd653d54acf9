import Header from "@/components/header/Header.jsx";
import Footer from "@/components/footer/Footer.jsx";
import Image from "next/image";
import {
  FaAward,
  FaUsers,
  FaGlobe,
  FaHeart,
  FaCertificate,
  FaShieldAlt,
  FaHandshake,
  FaStar,
} from "react-icons/fa";

export const metadata = {
  title: "À Propos - Bouguerrouche Travel | Agence de Voyage Alger",
  description:
    "Découvrez l'histoire de Bouguerrouche Travel, votre agence de voyage de confiance en Algérie. Plus de 15 ans d'expérience dans le tourisme.",
  keywords:
    "à propos Bouguerrouche Travel, agence voyage Alger, histoire entreprise, équipe",
};

const stats = [
  {
    icon: <FaUsers className="text-3xl text-sky-600" />,
    number: "5000+",
    label: "Clients Satisfaits",
  },
  {
    icon: <FaGlobe className="text-3xl text-sky-600" />,
    number: "15+",
    label: "Années d'Expérience",
  },
  {
    icon: <FaAward className="text-3xl text-sky-600" />,
    number: "50+",
    label: "Destinations",
  },
  {
    icon: <FaHeart className="text-3xl text-sky-600" />,
    number: "98%",
    label: "Taux de Satisfaction",
  },
];

const team = [
  {
    name: "-",
    position: "-",
    description:
      "--",
    image: "/images/back.png",
  },
  {
    name: "--",
    position: "--",
    description:
      "--",
    image: "/images/back.png",
  },
  {
    name: "--",
    position: "--",
    description:
      "--",
    image: "/images/back.png",
  },
  {
    name: "--",
    position: "---",
    description:
      "--",
    image: "/images/back.png",
  },
];

const certifications = [
  {
    icon: <FaCertificate className="text-2xl text-sky-600" />,
    title: "Licence Agence de Voyage",
    description: "Agréée par le Ministère du Tourisme algérien",
  },
  {
    icon: <FaShieldAlt className="text-2xl text-sky-600" />,
    title: "Assurance Responsabilité",
    description: "Couverture complète pour tous nos voyageurs",
  },
  {
    icon: <FaHandshake className="text-2xl text-sky-600" />,
    title: "Partenaire IATA",
    description: "Membre de l'Association Internationale du Transport Aérien",
  },
  {
    icon: <FaStar className="text-2xl text-sky-600" />,
    title: "Certification Qualité",
    description: "Standards internationaux de service",
  },
];

const values = [
  {
    title: "Excellence",
    description:
      "Nous nous engageons à fournir des services de la plus haute qualité à chacun de nos clients.",
    icon: "🏆",
  },
  {
    title: "Confiance",
    description:
      "La transparence et l'honnêteté sont au cœur de toutes nos relations avec nos clients.",
    icon: "🤝",
  },
  {
    title: "Innovation",
    description:
      "Nous adoptons les dernières technologies pour améliorer constamment votre expérience voyage.",
    icon: "💡",
  },
  {
    title: "Passion",
    description:
      "Notre amour pour les voyages et la découverte guide chacune de nos recommandations.",
    icon: "❤️",
  },
];

export default function About() {
  return (
    <>
      <Header />
      <main className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <section className="relative h-[60vh] md:h-[80vh] min-h-[400px] w-full overflow-hidden flex items-center justify-center">
          <Image
            src="/images/landingpagebackground.svg"
            alt="À Propos background"
            fill
            className="object-cover z-0"
            priority
            sizes="100vw"
          />
          <div className="relative z-10 text-center text-white px-4">
            <h1 className="text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-extrabold mb-2 leading-tight tracking-tight font-poppins">
              À Propos de Nous
            </h1>
            <hr className="border-t border-white w-full md:w-[90%] mb-6 mt-2 opacity-80 mx-auto" />
            <p className="text-lg sm:text-xl text-gray-200 mb-8 sm:mb-10 leading-relaxed max-w-3xl mx-auto font-poppins">
              Votre partenaire de confiance pour des voyages inoubliables depuis
              2009
            </p>
          </div>
        </section>

        {/* Company Story */}
        <section className="py-16 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-[40px] font-bold text-center mb-10 tracking-tight text-black font-poppins">
                  Notre Histoire
                </h2>
                <div className="space-y-4 text-gray-600 leading-relaxed font-poppins">
                  <p>
                    Fondée en 2009 par Mohamed Bouguerrouche, Bouguerrouche
                    Travel est née d&apos;une passion profonde pour les voyages
                    et d&apos;un désir de faire découvrir les merveilles de la
                    Tunisie aux voyageurs algériens.
                  </p>
                  <p>
                    Depuis nos débuts modestes avec une petite équipe dévouée,
                    nous avons grandi pour devenir l&apos;une des agences de
                    voyage les plus respectées d&apos;Alger, spécialisée dans
                    les destinations tunisiennes.
                  </p>
                  <p>
                    Aujourd&apos;hui, avec plus de 15 années d&apos;expérience,
                    nous avons accompagné plus de 5000 voyageurs dans leurs
                    découvertes, créant des souvenirs inoubliables et des
                    expériences authentiques.
                  </p>
                </div>
              </div>
              <div className="relative h-96 rounded-xl overflow-hidden shadow-sm">
                <Image
                  src="/images/ExploreTunisia/exploretunisia1.jpg"
                  alt="Histoire Bouguerrouche Travel"
                  fill
                  className="object-cover"
                  sizes="(max-width: 1024px) 100vw, 50vw"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Statistics */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4">
            <h2 className="text-[40px] font-bold text-center mb-10 tracking-tight text-black font-poppins">
              Nos Chiffres Clés
            </h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="flex justify-center mb-4">{stat.icon}</div>
                  <div className="text-3xl md:text-4xl font-bold text-black mb-2 font-poppins">
                    {stat.number}
                  </div>
                  <div className="text-gray-600 font-medium font-poppins">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Mission & Vision */}
        <section className="py-16 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
              <div className="bg-white rounded-xl shadow-sm p-8">
                <h3 className="text-2xl font-bold text-black mb-4 font-poppins">
                  Notre Mission
                </h3>
                <p className="text-gray-600 leading-relaxed font-poppins">
                  Offrir à nos clients des expériences de voyage exceptionnelles
                  en Tunisie, en combinant un service personnalisé, une
                  expertise locale approfondie et des tarifs compétitifs. Nous
                  nous engageons à créer des souvenirs durables tout en
                  respectant les cultures locales et l&apos;environnement.
                </p>
              </div>
              <div className="bg-white rounded-xl shadow-sm p-8">
                <h3 className="text-2xl font-bold text-black mb-4 font-poppins">
                  Notre Vision
                </h3>
                <p className="text-gray-600 leading-relaxed font-poppins">
                  Devenir la référence en matière de voyages vers la Tunisie
                  pour les voyageurs algériens, en étant reconnus pour notre
                  excellence, notre innovation et notre contribution au
                  développement du tourisme durable entre l&apos;Algérie et la
                  Tunisie.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Values */}
        <section className="py-16 bg-sky-50">
          <div className="max-w-7xl mx-auto px-4">
            <h2 className="text-[40px] font-bold text-center mb-10 tracking-tight text-black font-poppins">
              Nos Valeurs
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {values.map((value, index) => (
                <div
                  key={index}
                  className="bg-white rounded-xl shadow-sm p-6 text-center"
                >
                  <div className="text-4xl mb-4">{value.icon}</div>
                  <h3 className="text-xl font-bold text-black mb-3 font-poppins">
                    {value.title}
                  </h3>
                  <p className="text-gray-600 text-sm leading-relaxed font-poppins">
                    {value.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Team */}
        <section className="py-16 px-4">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-[40px] font-bold text-center mb-10 tracking-tight text-black font-poppins">
              Notre Équipe
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {team.map((member, index) => (
                <div
                  key={index}
                  className="bg-white rounded-xl shadow-sm overflow-hidden"
                >
                  <div className="relative h-64">
                    <Image
                      src={member.image}
                      alt={member.name}
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                    />
                  </div>
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-black mb-2 font-poppins">
                      {member.name}
                    </h3>
                    <p className="text-sky-600 font-medium mb-3 font-poppins">
                      {member.position}
                    </p>
                    <p className="text-gray-600 text-sm leading-relaxed font-poppins">
                      {member.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Certifications */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4">
            <h2 className="text-[40px] font-bold text-center mb-10 tracking-tight text-black font-poppins">
              Nos Certifications
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {certifications.map((cert, index) => (
                <div
                  key={index}
                  className="bg-white rounded-xl shadow-sm p-6 text-center"
                >
                  <div className="flex justify-center mb-4">{cert.icon}</div>
                  <h3 className="text-lg font-bold text-black mb-2 font-poppins">
                    {cert.title}
                  </h3>
                  <p className="text-gray-600 text-sm font-poppins">
                    {cert.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="bg-blue-600 py-16">
          <div className="max-w-4xl mx-auto text-center px-4">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Prêt à Voyager avec Nous ?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Découvrez pourquoi des milliers de voyageurs nous font confiance
              pour leurs vacances
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/contact"
                className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
              >
                Nous Contacter
              </a>
              <a
                href="/destinations"
                className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors duration-200"
              >
                Voir Nos Destinations
              </a>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
