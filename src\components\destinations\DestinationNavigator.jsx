"use client";
import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import { PageLoading } from "@/components/ui/LoadingSpinner";
import { ArrowLeft, MapPin, Building, Star } from "lucide-react";
import SafeImage from "@/components/ui/SafeImage";
import {
  getCountriesClient,
  getCitiesByCountryClient,
  getHotelsByCityClient
} from "@/lib/destinations-client";

const NAVIGATION_LEVELS = {
  COUNTRIES: 'countries',
  CITIES: 'cities',
  HOTELS: 'hotels'
};

export default function DestinationNavigator({ onHotelSelect }) {
  const [currentLevel, setCurrentLevel] = useState(NAVIGATION_LEVELS.COUNTRIES);
  const [selectedCountry, setSelectedCountry] = useState(null);
  const [selectedCity, setSelectedCity] = useState(null);
  
  const [countries, setCountries] = useState([]);
  const [cities, setCities] = useState([]);
  const [hotels, setHotels] = useState([]);
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load countries on component mount
  useEffect(() => {
    loadCountries();
  }, []);

  const loadCountries = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const { data, error } = await getCountriesClient();
      if (error) throw error;
      
      setCountries(data);
    } catch (err) {
      console.error('Error loading countries:', err);
      setError('Erreur lors du chargement des pays');
    } finally {
      setLoading(false);
    }
  };

  const loadCities = async (countryId) => {
    setLoading(true);
    setError(null);
    
    try {
      const { data, error } = await getCitiesByCountryClient(countryId);
      if (error) throw error;
      
      setCities(data);
    } catch (err) {
      console.error('Error loading cities:', err);
      setError('Erreur lors du chargement des villes');
    } finally {
      setLoading(false);
    }
  };

  const loadHotels = async (cityId) => {
    setLoading(true);
    setError(null);
    
    try {
      const { data, error } = await getHotelsByCityClient(cityId);
      if (error) throw error;
      
      setHotels(data);
    } catch (err) {
      console.error('Error loading hotels:', err);
      setError('Erreur lors du chargement des hôtels');
    } finally {
      setLoading(false);
    }
  };

  const handleCountrySelect = async (country) => {
    setSelectedCountry(country);
    setCurrentLevel(NAVIGATION_LEVELS.CITIES);
    await loadCities(country.id);
  };

  const handleCitySelect = async (city) => {
    setSelectedCity(city);
    setCurrentLevel(NAVIGATION_LEVELS.HOTELS);
    await loadHotels(city.id);
  };

  const handleBackToCountries = () => {
    setCurrentLevel(NAVIGATION_LEVELS.COUNTRIES);
    setSelectedCountry(null);
    setSelectedCity(null);
    setCities([]);
    setHotels([]);
  };

  const handleBackToCities = () => {
    setCurrentLevel(NAVIGATION_LEVELS.CITIES);
    setSelectedCity(null);
    setHotels([]);
  };

  const getBreadcrumb = () => {
    const items = ['Destinations'];
    
    if (selectedCountry) {
      items.push(selectedCountry.name);
    }
    
    if (selectedCity) {
      items.push(selectedCity.name);
    }
    
    return items.join(' > ');
  };

  if (loading) {
    return (
      <div className="animate-fade-in">
        <PageLoading message="Chargement des destinations..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12 animate-fade-in">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <span className="text-red-500 text-2xl">⚠️</span>
        </div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">Erreur de chargement</h3>
        <p className="text-gray-600 mb-4">{error}</p>
        <Button
          onClick={currentLevel === NAVIGATION_LEVELS.COUNTRIES ? loadCountries :
                   currentLevel === NAVIGATION_LEVELS.CITIES ? () => loadCities(selectedCountry.id) :
                   () => loadHotels(selectedCity.id)}
          variant="primary"
          className="transition-all duration-200 hover:scale-105"
        >
          Réessayer
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb and Navigation */}
      <div className="flex items-center justify-between animate-fade-in">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <MapPin className="w-4 h-4" />
          <span>{getBreadcrumb()}</span>
        </div>

        {currentLevel !== NAVIGATION_LEVELS.COUNTRIES && (
          <Button
            variant="outline"
            size="sm"
            icon={<ArrowLeft className="w-4 h-4" />}
            onClick={currentLevel === NAVIGATION_LEVELS.CITIES ? handleBackToCountries : handleBackToCities}
            className="transition-all duration-200 hover:scale-105"
          >
            Retour
          </Button>
        )}
      </div>

      {/* Content based on current level with smooth transitions */}
      <div className="transition-all duration-500 ease-in-out">
        {currentLevel === NAVIGATION_LEVELS.COUNTRIES && (
          <div className="animate-slide-in-from-right">
            <CountriesGrid countries={countries} onCountrySelect={handleCountrySelect} />
          </div>
        )}

        {currentLevel === NAVIGATION_LEVELS.CITIES && (
          <div className="animate-slide-in-from-right">
            <CitiesGrid cities={cities} onCitySelect={handleCitySelect} />
          </div>
        )}

        {currentLevel === NAVIGATION_LEVELS.HOTELS && (
          <div className="animate-slide-in-from-right">
            <HotelsGrid hotels={hotels} onHotelSelect={onHotelSelect} />
          </div>
        )}
      </div>
    </div>
  );
}

// Countries Grid Component
function CountriesGrid({ countries, onCountrySelect }) {
  if (countries.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Aucun pays disponible pour le moment.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {countries.map((country, index) => (
        <Card
          key={country.id}
          className="travel-card-shadow cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-xl animate-fade-in-up"
          style={{ animationDelay: `${index * 100}ms` }}
          hover={true}
          onClick={() => onCountrySelect(country)}
        >
          <CardContent className="p-6 text-center">
            <div className="mb-4">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {country.name}
              </h3>
              {country.is_featured && (
                <Badge variant="secondary" className="mb-2">
                  Destination vedette
                </Badge>
              )}
              <p className="text-sm text-gray-600">
                {country.cities?.length || 0} ville(s) disponible(s)
              </p>
            </div>
            <Button variant="primary" size="sm" className="w-full">
              Explorer {country.name}
            </Button>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

// Cities Grid Component  
function CitiesGrid({ cities, onCitySelect }) {
  if (cities.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Aucune ville disponible dans ce pays.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {cities.map((city, index) => (
        <Card
          key={city.id}
          className="travel-card-shadow cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-xl animate-fade-in-up"
          style={{ animationDelay: `${index * 100}ms` }}
          hover={true}
          onClick={() => onCitySelect(city)}
        >
          <div className="relative w-full h-48">
            <SafeImage
              src={city.image_url}
              alt={city.name}
              fill
              className="object-cover rounded-t-lg"
              sizes="(max-width: 768px) 100vw, 33vw"
            />
            {city.is_featured && (
              <div className="absolute top-3 left-3">
                <Badge variant="secondary" className="shadow-md">
                  Ville vedette
                </Badge>
              </div>
            )}
          </div>
          
          <CardContent className="p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {city.name}
            </h3>
            {city.region && (
              <p className="text-sm text-gray-600 mb-2">{city.region}</p>
            )}
            {city.description && (
              <p className="text-xs text-gray-500 mb-3 line-clamp-2">
                {city.description}
              </p>
            )}
            <Button variant="primary" size="sm" className="w-full">
              Voir les hôtels
            </Button>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

// Hotels Grid Component
function HotelsGrid({ hotels, onHotelSelect }) {
  if (hotels.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Aucun hôtel disponible dans cette ville.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {hotels.map((hotel, index) => (
        <Card
          key={hotel.id}
          className="travel-card-shadow transition-all duration-300 hover:scale-105 hover:shadow-xl animate-fade-in-up"
          style={{ animationDelay: `${index * 100}ms` }}
          hover={true}
        >
          <div className="relative w-full h-48">
            <SafeImage
              src={hotel.image_url}
              alt={hotel.name}
              fill
              className="object-cover rounded-t-lg"
              sizes="(max-width: 768px) 100vw, 33vw"
            />
            {hotel.is_featured && (
              <div className="absolute top-3 left-3">
                <Badge variant="secondary" className="shadow-md">
                  Hôtel vedette
                </Badge>
              </div>
            )}
            {hotel.discount_percentage > 0 && (
              <div className="absolute top-3 right-3">
                <Badge variant="accent" className="shadow-md">
                  -{hotel.discount_percentage}%
                </Badge>
              </div>
            )}
          </div>
          
          <CardContent className="p-4">
            <div className="flex items-start justify-between mb-2">
              <h3 className="text-lg font-semibold text-gray-900">
                {hotel.name}
              </h3>
              {hotel.stars && (
                <div className="flex items-center gap-1">
                  {[...Array(hotel.stars)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
              )}
            </div>
            
            {hotel.description && (
              <p className="text-xs text-gray-500 mb-3 line-clamp-2">
                {hotel.description}
              </p>
            )}
            
            <div className="flex items-center justify-between">
              <div>
                <span className="text-lg font-bold text-primary">
                  {hotel.price?.toLocaleString()} {hotel.currency}
                </span>
                <span className="text-xs text-gray-500 block">par nuit</span>
              </div>
              
              <Button
                variant="primary"
                size="sm"
                onClick={() => onHotelSelect(hotel)}
              >
                Réserver maintenant
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
