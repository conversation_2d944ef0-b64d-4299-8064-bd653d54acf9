"use client";
import { useState } from "react";
import { Card, CardContent } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import SafeImage from "@/components/ui/SafeImage";
import { Star, MapPin, Eye, Wifi, Car, Waves } from "lucide-react";
import { useRouter } from "next/navigation";

const amenityIcons = {
  'WiFi': Wifi,
  'Parking': Car,
  'Pool': Waves,
  'Piscine': Waves,
  'Wi-Fi': Wifi,
  'Stationnement': Car
};

export default function HotelGrid({ 
  hotels = [], 
  loading = false, 
  error = null,
  onLoadMore = null,
  hasMore = false,
  loadingMore = false
}) {
  const router = useRouter();

  const handleExploreHotel = (hotel) => {
    // Create URL parameters for the offers page
    const params = new URLSearchParams({
      hotel_id: hotel.id,
      hotel_name: encodeURIComponent(hotel.name),
      city: encodeURIComponent(hotel.cities?.name || ''),
      country: encodeURIComponent(hotel.cities?.countries?.name || ''),
      stars: hotel.stars || '',
      price: hotel.price || '',
      currency: hotel.currency || 'DA',
      description: encodeURIComponent(hotel.description || ''),
      image: encodeURIComponent(hotel.image_url || ''),
      amenities: encodeURIComponent(JSON.stringify(hotel.amenities || [])),
      highlights: encodeURIComponent(JSON.stringify(hotel.highlights || [])),
      discount: hotel.discount_percentage || 0,
      offer_text: encodeURIComponent(hotel.offer_text || '')
    });

    router.push(`/offerspage?${params.toString()}`);
  };

  const getAmenityIcon = (amenity) => {
    const IconComponent = amenityIcons[amenity];
    return IconComponent ? <IconComponent className="w-3 h-3" /> : null;
  };

  if (loading && hotels.length === 0) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, index) => (
          <Card key={index} className="animate-pulse">
            <div className="w-full h-48 bg-gray-200 rounded-t-lg"></div>
            <CardContent className="p-4 space-y-3">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              <div className="h-3 bg-gray-200 rounded w-full"></div>
              <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <span className="text-red-500 text-2xl">⚠️</span>
        </div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">Erreur de chargement</h3>
        <p className="text-gray-600 mb-4">{error}</p>
        <Button onClick={() => window.location.reload()} variant="primary">
          Réessayer
        </Button>
      </div>
    );
  }

  if (hotels.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <span className="text-gray-400 text-2xl">🏨</span>
        </div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">Aucun hôtel trouvé</h3>
        <p className="text-gray-600">
          Essayez de modifier vos critères de recherche ou vos filtres.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {hotels.map((hotel, index) => (
          <Card
            key={hotel.id}
            className="travel-card-shadow transition-all duration-300 hover:scale-105 hover:shadow-xl animate-fade-in-up"
            style={{ animationDelay: `${index * 100}ms` }}
            hover={true}
          >
            <div className="relative w-full h-48">
              <SafeImage
                src={hotel.image_url}
                alt={hotel.name}
                fill
                className="object-cover rounded-t-lg"
                sizes="(max-width: 768px) 100vw, 33vw"
              />
              
              {/* Badges */}
              <div className="absolute top-3 left-3 flex flex-col gap-2">
                {hotel.is_featured && (
                  <Badge variant="secondary" className="shadow-md">
                    Vedette
                  </Badge>
                )}
                {hotel.discount_percentage > 0 && (
                  <Badge variant="accent" className="shadow-md">
                    -{hotel.discount_percentage}%
                  </Badge>
                )}
              </div>

              {/* Location */}
              <div className="absolute top-3 right-3">
                <Badge variant="primary" className="shadow-md flex items-center gap-1">
                  <MapPin className="w-3 h-3" />
                  {hotel.cities?.name}
                </Badge>
              </div>
            </div>
            
            <CardContent className="p-4">
              <div className="flex items-start justify-between mb-2">
                <h3 className="text-lg font-semibold text-gray-900 line-clamp-1">
                  {hotel.name}
                </h3>
                {hotel.stars && (
                  <div className="flex items-center gap-1 ml-2">
                    {[...Array(hotel.stars)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                )}
              </div>
              
              {/* Location Details */}
              <div className="text-sm text-gray-600 mb-2">
                {hotel.cities?.name}, {hotel.cities?.countries?.name}
              </div>
              
              {/* Description */}
              {hotel.description && (
                <p className="text-xs text-gray-500 mb-3 line-clamp-2">
                  {hotel.description}
                </p>
              )}

              {/* Amenities */}
              {hotel.amenities && hotel.amenities.length > 0 && (
                <div className="mb-3">
                  <div className="flex flex-wrap gap-1">
                    {hotel.amenities.slice(0, 4).map((amenity, idx) => (
                      <div
                        key={idx}
                        className="flex items-center gap-1 bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs"
                      >
                        {getAmenityIcon(amenity)}
                        <span>{amenity}</span>
                      </div>
                    ))}
                    {hotel.amenities.length > 4 && (
                      <div className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
                        +{hotel.amenities.length - 4}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Special Offer */}
              {hotel.offer_text && (
                <div className="mb-3">
                  <Badge variant="accent" size="sm" className="text-xs">
                    {hotel.offer_text}
                  </Badge>
                </div>
              )}
              
              {/* Price and Action */}
              <div className="flex items-center justify-between">
                <div>
                  {hotel.discount_percentage > 0 && (
                    <div className="text-xs text-gray-500 line-through">
                      {Math.round(hotel.price * (1 + hotel.discount_percentage / 100))} {hotel.currency}
                    </div>
                  )}
                  <div className="flex items-baseline gap-1">
                    <span className="text-lg font-bold text-primary">
                      {hotel.price?.toLocaleString()} {hotel.currency}
                    </span>
                    <span className="text-xs text-gray-500">par nuit</span>
                  </div>
                </div>
                
                <Button
                  variant="primary"
                  size="sm"
                  icon={<Eye className="w-4 h-4" />}
                  onClick={() => handleExploreHotel(hotel)}
                  className="transition-all duration-200 hover:scale-105"
                >
                  Explorer
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Load More Button */}
      {hasMore && onLoadMore && (
        <div className="text-center pt-6">
          <Button
            onClick={onLoadMore}
            variant="outline"
            loading={loadingMore}
            className="px-8"
          >
            {loadingMore ? 'Chargement...' : 'Voir plus d\'hôtels'}
          </Button>
        </div>
      )}
    </div>
  );
}
