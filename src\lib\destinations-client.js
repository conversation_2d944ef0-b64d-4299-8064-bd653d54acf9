/**
 * Client-side functions for destinations API
 */

/**
 * Fetch all countries with cities that have active hotels
 */
export async function getCountriesClient() {
  try {
    const response = await fetch('/api/destinations/countries');
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to fetch countries');
    }

    const responseData = await response.json();
    return { data: responseData.data || [], error: null };
  } catch (error) {
    console.error('Error fetching countries:', error);
    return { data: [], error };
  }
}

/**
 * Fetch cities by country ID
 */
export async function getCitiesByCountryClient(countryId, featuredOnly = false) {
  try {
    const params = new URLSearchParams({
      country_id: countryId,
      featured: featuredOnly.toString()
    });

    const response = await fetch(`/api/destinations/cities?${params}`);
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to fetch cities');
    }

    const responseData = await response.json();
    return { data: responseData.data || [], error: null };
  } catch (error) {
    console.error('Error fetching cities:', error);
    return { data: [], error };
  }
}

/**
 * Fetch hotels by city ID
 */
export async function getHotelsByCityClient(cityId, featuredOnly = false) {
  try {
    const params = new URLSearchParams({
      city_id: cityId,
      featured: featuredOnly.toString()
    });

    const response = await fetch(`/api/destinations/hotels?${params}`);
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to fetch hotels');
    }

    const responseData = await response.json();
    return { data: responseData.data || [], error: null };
  } catch (error) {
    console.error('Error fetching hotels:', error);
    return { data: [], error };
  }
}
