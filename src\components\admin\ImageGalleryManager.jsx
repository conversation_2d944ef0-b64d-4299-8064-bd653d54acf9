"use client";
import { useState, useRef } from 'react';
import { Trash2, GripVertical, Eye, Edit3, Star, X } from 'lucide-react';
import Image from 'next/image';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { getOptimizedImageUrl } from '@/lib/cloudinary-client';

export const ImageGalleryManager = ({ 
  images = [], 
  onImagesChange, 
  onSetPrimary,
  primaryImageId = null,
  className = '' 
}) => {
  const [draggedIndex, setDraggedIndex] = useState(null);
  const [editingImage, setEditingImage] = useState(null);
  const [previewImage, setPreviewImage] = useState(null);
  const dragCounter = useRef(0);

  const handleDragStart = (e, index) => {
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/html', e.target.outerHTML);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDragEnter = (e) => {
    e.preventDefault();
    dragCounter.current++;
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    dragCounter.current--;
  };

  const handleDrop = (e, dropIndex) => {
    e.preventDefault();
    dragCounter.current = 0;
    
    if (draggedIndex === null || draggedIndex === dropIndex) {
      setDraggedIndex(null);
      return;
    }

    const newImages = [...images];
    const draggedImage = newImages[draggedIndex];
    
    // Remove the dragged item
    newImages.splice(draggedIndex, 1);
    
    // Insert at new position
    const insertIndex = draggedIndex < dropIndex ? dropIndex - 1 : dropIndex;
    newImages.splice(insertIndex, 0, draggedImage);
    
    // Update display orders
    const updatedImages = newImages.map((img, index) => ({
      ...img,
      display_order: index
    }));
    
    onImagesChange(updatedImages);
    setDraggedIndex(null);
  };

  const handleDragEnd = () => {
    setDraggedIndex(null);
    dragCounter.current = 0;
  };

  const removeImage = (index) => {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette image ?')) {
      const newImages = images.filter((_, i) => i !== index);
      onImagesChange(newImages);
    }
  };

  const updateImageData = (index, field, value) => {
    const newImages = [...images];
    newImages[index] = {
      ...newImages[index],
      [field]: value
    };
    onImagesChange(newImages);
  };

  const setPrimaryImage = (index) => {
    const image = images[index];
    if (onSetPrimary) {
      onSetPrimary(image);
    }
  };

  if (images.length === 0) {
    return (
      <div className={`text-center py-8 text-gray-500 ${className}`}>
        <Eye className="w-12 h-12 mx-auto mb-4 text-gray-300" />
        <p>Aucune image dans la galerie</p>
        <p className="text-sm">Téléchargez des images pour commencer</p>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">
          Galerie d&apos;images ({images.length})
        </h3>
        <p className="text-sm text-gray-500">
          Glissez-déposez pour réorganiser
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {images.map((image, index) => (
          <div
            key={image.id || index}
            draggable
            onDragStart={(e) => handleDragStart(e, index)}
            onDragOver={handleDragOver}
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            onDrop={(e) => handleDrop(e, index)}
            onDragEnd={handleDragEnd}
            className={`
              relative group bg-white border rounded-lg overflow-hidden shadow-sm
              transition-all duration-200 cursor-move
              ${draggedIndex === index ? 'opacity-50 scale-95' : 'hover:shadow-md'}
              ${primaryImageId === image.public_id ? 'ring-2 ring-blue-500' : ''}
            `}
          >
            {/* Drag Handle */}
            <div className="absolute top-2 left-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
              <div className="bg-black/50 rounded p-1">
                <GripVertical className="w-4 h-4 text-white" />
              </div>
            </div>

            {/* Primary Image Badge */}
            {primaryImageId === image.public_id && (
              <div className="absolute top-2 right-2 z-10">
                <div className="bg-blue-500 text-white px-2 py-1 rounded text-xs font-medium flex items-center gap-1">
                  <Star className="w-3 h-3" />
                  Principal
                </div>
              </div>
            )}

            {/* Image */}
            <div className="relative aspect-video">
              <Image
                src={getOptimizedImageUrl(image.public_id, { width: 400, height: 300 })}
                alt={image.alt || `Image ${index + 1}`}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
              
              {/* Overlay Actions */}
              <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  className="bg-white/90 hover:bg-white"
                  onClick={() => setPreviewImage(image)}
                >
                  <Eye className="w-4 h-4" />
                </Button>
                
                <Button
                  size="sm"
                  variant="outline"
                  className="bg-white/90 hover:bg-white"
                  onClick={() => setEditingImage(index)}
                >
                  <Edit3 className="w-4 h-4" />
                </Button>
                
                {primaryImageId !== image.public_id && (
                  <Button
                    size="sm"
                    variant="outline"
                    className="bg-blue-500/90 hover:bg-blue-600 text-white border-blue-500"
                    onClick={() => setPrimaryImage(index)}
                  >
                    <Star className="w-4 h-4" />
                  </Button>
                )}
                
                <Button
                  size="sm"
                  variant="outline"
                  className="bg-red-500/90 hover:bg-red-600 text-white border-red-500"
                  onClick={() => removeImage(index)}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Image Info */}
            <div className="p-3">
              <div className="text-sm text-gray-600 mb-1">
                Position: {index + 1}
              </div>
              {image.alt && (
                <div className="text-sm text-gray-800 truncate">
                  {image.alt}
                </div>
              )}
              {image.caption && (
                <div className="text-xs text-gray-500 truncate">
                  {image.caption}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Edit Modal */}
      {editingImage !== null && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-semibold mb-4">Modifier l&apos;image</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Texte alternatif
                </label>
                <Input
                  value={images[editingImage]?.alt || ''}
                  onChange={(e) => updateImageData(editingImage, 'alt', e.target.value)}
                  placeholder="Description de l'image"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Légende
                </label>
                <Input
                  value={images[editingImage]?.caption || ''}
                  onChange={(e) => updateImageData(editingImage, 'caption', e.target.value)}
                  placeholder="Légende de l'image"
                />
              </div>
            </div>
            
            <div className="flex justify-end gap-2 mt-6">
              <Button
                variant="outline"
                onClick={() => setEditingImage(null)}
              >
                Annuler
              </Button>
              <Button
                variant="primary"
                onClick={() => setEditingImage(null)}
              >
                Sauvegarder
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Preview Modal */}
      {previewImage && (
        <div className="fixed inset-0 bg-black/90 flex items-center justify-center z-50 p-4">
          <div className="relative max-w-4xl max-h-full">
            <button
              onClick={() => setPreviewImage(null)}
              className="absolute top-4 right-4 text-white hover:text-gray-300 z-10"
            >
              <X className="w-8 h-8" />
            </button>
            
            <Image
              src={getOptimizedImageUrl(previewImage.public_id, { width: 1200, height: 900 })}
              alt={previewImage.alt || 'Preview'}
              width={1200}
              height={900}
              className="max-w-full max-h-full object-contain"
            />
            
            {(previewImage.alt || previewImage.caption) && (
              <div className="absolute bottom-4 left-4 right-4 bg-black/70 text-white p-4 rounded">
                {previewImage.alt && (
                  <div className="font-medium">{previewImage.alt}</div>
                )}
                {previewImage.caption && (
                  <div className="text-sm text-gray-300">{previewImage.caption}</div>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageGalleryManager;
