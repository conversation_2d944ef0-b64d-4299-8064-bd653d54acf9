import { NextResponse } from "next/server";
import { createServerSupabaseClient } from "@/lib/auth";

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const cityId = searchParams.get("city_id");
    const featuredOnly = searchParams.get("featured") === "true";

    if (!cityId) {
      return NextResponse.json(
        { error: "City ID is required" },
        { status: 400 }
      );
    }

    const supabase = await createServerSupabaseClient();

    let query = supabase
      .from("hotels")
      .select(
        `
        id,
        name,
        stars,
        description,
        price,
        currency,
        image_url,
        image_url_1,
        image_url_2,
        image_url_3,
        image_url_4,
        discount_percentage,
        offer_text,
        highlights,
        amenities,
        is_featured,
        cities (
          id,
          name,
          region,
          countries (
            id,
            name,
            code,
            currency
          )
        )
      `
      )
      .eq("city_id", cityId)
      .eq("is_active", true);

    if (featuredOnly) {
      query = query.eq("is_featured", true);
    }

    query = query.order("name");

    const { data: hotels, error } = await query;

    if (error) {
      console.error("Error fetching hotels:", error);
      return NextResponse.json(
        { error: "Failed to fetch hotels" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: hotels || [],
    });
  } catch (error) {
    console.error("API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
