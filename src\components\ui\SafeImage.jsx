"use client";
import { useState } from "react";
import Image from "next/image";

/**
 * SafeImage component with automatic fallback handling
 * Provides a clean way to handle external images with error fallbacks
 */
export default function SafeImage({
  src,
  alt,
  fallbackSrc = "/images/back.png",
  className = "",
  fill = false,
  width,
  height,
  sizes,
  priority = false,
  ...props
}) {
  const [imgSrc, setImgSrc] = useState(src);
  const [hasError, setHasError] = useState(false);

  const handleError = () => {
    if (!hasError && imgSrc !== fallbackSrc) {
      setHasError(true);
      setImgSrc(fallbackSrc);
    }
  };

  const handleLoad = () => {
    setHasError(false);
  };

  const imageProps = {
    src: imgSrc || fallbackSrc,
    alt: alt || "Image",
    className,
    onError: handleError,
    onLoad: handleLoad,
    priority,
    ...props
  };

  if (fill) {
    imageProps.fill = true;
    if (sizes) imageProps.sizes = sizes;
  } else {
    imageProps.width = width || 400;
    imageProps.height = height || 300;
  }

  return <Image {...imageProps} />;
}
