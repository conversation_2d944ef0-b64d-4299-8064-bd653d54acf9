/**
 * Admin API utilities with automatic token refresh
 * These functions should be used in admin components instead of direct database calls
 */

import * as db from './database';

/**
 * Wrapper function to make database operations with automatic retry on auth failure
 */
async function withAuthRetry(operation, maxRetries = 1) {
  let lastError;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      // Check if it's an auth-related error
      const isAuthError = 
        error.message?.includes('JWT') || 
        error.message?.includes('auth') || 
        error.message?.includes('unauthorized') ||
        error.code === 'PGRST301' || // JWT expired
        error.code === 'PGRST302'; // JWT invalid
      
      if (isAuthError && attempt < maxRetries) {
        console.log(`Auth error detected (attempt ${attempt + 1}), retrying...`);
        // Wait a bit before retry to allow token refresh
        await new Promise(resolve => setTimeout(resolve, 1000));
        continue;
      }
      
      throw error;
    }
  }
  
  throw lastError;
}

// ===== COUNTRIES =====
export const getCountries = (featuredOnly = false) => 
  withAuthRetry(() => db.getCountries(featuredOnly));

export const createCountry = (countryData) => 
  withAuthRetry(() => db.createCountry(countryData));

export const updateCountry = (id, countryData) => 
  withAuthRetry(() => db.updateCountry(id, countryData));

export const deleteCountry = (id) => 
  withAuthRetry(() => db.deleteCountry(id));

// ===== CITIES =====
export const getCities = (countryId = null, featuredOnly = false) => 
  withAuthRetry(() => db.getCities(countryId, featuredOnly));

export const createCity = (cityData) => 
  withAuthRetry(() => db.createCity(cityData));

export const updateCity = (id, cityData) => 
  withAuthRetry(() => db.updateCity(id, cityData));

export const deleteCity = (id) => 
  withAuthRetry(() => db.deleteCity(id));

// ===== HOTELS =====
export const getHotels = (cityId = null, featuredOnly = false, activeOnly = true) => 
  withAuthRetry(() => db.getHotels(cityId, featuredOnly, activeOnly));

export const getHotelById = (id) => 
  withAuthRetry(() => db.getHotelById(id));

export const createHotel = (hotelData) => 
  withAuthRetry(() => db.createHotel(hotelData));

export const updateHotel = (id, hotelData) => 
  withAuthRetry(() => db.updateHotel(id, hotelData));

export const deleteHotel = (id) => 
  withAuthRetry(() => db.deleteHotel(id));

export const toggleTopBookNow = (id) => 
  withAuthRetry(() => db.toggleTopBookNow(id));

// ===== CUSTOMERS =====
export const getCustomers = () => 
  withAuthRetry(() => db.getCustomers());

export const createCustomer = (customerData) => 
  withAuthRetry(() => db.createCustomer(customerData));

export const updateCustomer = (id, customerData) => 
  withAuthRetry(() => db.updateCustomer(id, customerData));

export const deleteCustomer = (id) => 
  withAuthRetry(() => db.deleteCustomer(id));

// ===== BOOKINGS =====
export const getBookings = () => 
  withAuthRetry(() => db.getBookings());

export const getBookingById = (id) => 
  withAuthRetry(() => db.getBookingById(id));

export const createBooking = (bookingData) => 
  withAuthRetry(() => db.createBooking(bookingData));

export const updateBooking = (id, bookingData) => 
  withAuthRetry(() => db.updateBooking(id, bookingData));

export const deleteBooking = (id) => 
  withAuthRetry(() => db.deleteBooking(id));

// ===== NEWSLETTER =====
export const getNewsletterSubscribers = () => 
  withAuthRetry(() => db.getNewsletterSubscribers());

export const createNewsletterSubscriber = (subscriberData) => 
  withAuthRetry(() => db.createNewsletterSubscriber(subscriberData));

export const deleteNewsletterSubscriber = (id) => 
  withAuthRetry(() => db.deleteNewsletterSubscriber(id));

// ===== EMAIL CAMPAIGNS =====
export const getEmailCampaigns = () => 
  withAuthRetry(() => db.getEmailCampaigns());

export const createEmailCampaign = (campaignData) => 
  withAuthRetry(() => db.createEmailCampaign(campaignData));

export const updateEmailCampaign = (id, campaignData) => 
  withAuthRetry(() => db.updateEmailCampaign(id, campaignData));

export const deleteEmailCampaign = (id) => 
  withAuthRetry(() => db.deleteEmailCampaign(id));

// ===== SETTINGS =====
export const getSettings = () => 
  withAuthRetry(() => db.getSettings());

export const updateSettings = (settings) => 
  withAuthRetry(() => db.updateSettings(settings));

// ===== DASHBOARD =====
export const getDashboardStats = () => 
  withAuthRetry(() => db.getDashboardStats());
