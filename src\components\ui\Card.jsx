"use client";
import { forwardRef } from "react";
import { cn } from "@/lib/utils";

const Card = forwardRef(
  (
    {
      className,
      children,
      hover = false,
      interactive = false,
      padding = "default",
      ...props
    },
    ref
  ) => {
    const paddingClasses = {
      none: "",
      sm: "p-4",
      default: "p-6",
      lg: "p-8",
    };

    return (
      <div
        className={cn(
          "card",
          hover && "card-hover",
          interactive && "card-interactive",
          paddingClasses[padding] || paddingClasses.default,
          className
        )}
        ref={ref}
        {...props}
      >
        {children}
      </div>
    );
  }
);

Card.displayName = "Card";

const CardHeader = forwardRef(({ className, children, ...props }, ref) => (
  <div
    className={cn("flex flex-col space-y-1.5 pb-4", className)}
    ref={ref}
    {...props}
  >
    {children}
  </div>
));

CardHeader.displayName = "CardHeader";

const CardTitle = forwardRef(({ className, children, ...props }, ref) => (
  <h3
    className={cn("text-heading text-xl leading-none tracking-tight", className)}
    ref={ref}
    {...props}
  >
    {children}
  </h3>
));

CardTitle.displayName = "CardTitle";

const CardDescription = forwardRef(({ className, children, ...props }, ref) => (
  <p
    className={cn("text-body text-sm", className)}
    ref={ref}
    {...props}
  >
    {children}
  </p>
));

CardDescription.displayName = "CardDescription";

const CardContent = forwardRef(({ className, children, ...props }, ref) => (
  <div className={cn("", className)} ref={ref} {...props}>
    {children}
  </div>
));

CardContent.displayName = "CardContent";

const CardFooter = forwardRef(({ className, children, ...props }, ref) => (
  <div
    className={cn("flex items-center pt-4", className)}
    ref={ref}
    {...props}
  >
    {children}
  </div>
));

CardFooter.displayName = "CardFooter";

export { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter };
