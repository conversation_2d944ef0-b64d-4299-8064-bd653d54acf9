export async function GET(){const algerianAirports=["ALG","ORN","CZL","TLM","AAE","BJA","GHA","ELG"],tunisianAirports=["TUN","NBE","MIR","SFA","TOE"],today=(new Date).toISOString().split("T")[0];try{const tokenRes=await fetch("https://test.api.amadeus.com/v1/security/oauth2/token",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({grant_type:"client_credentials",client_id:process.env.AMADEUS_CLIENT_ID,client_secret:process.env.AMADEUS_CLIENT_SECRET})}),{access_token:access_token}=await tokenRes.json(),allFlights=[];for(const from of algerianAirports)for(const to of tunisianAirports){const res=await fetch(`https://test.api.amadeus.com/v2/shopping/flight-offers?originLocationCode=${from}&destinationLocationCode=${to}&departureDate=${today}&adults=1&max=2`,{headers:{Authorization:`Bearer ${access_token}`}}),data=await res.json();data.data&&allFlights.push(...data.data)}return Response.json({data:allFlights})}catch(error){return console.error("Error fetching flights:",error),new Response("Failed to fetch flights",{status:500})}}