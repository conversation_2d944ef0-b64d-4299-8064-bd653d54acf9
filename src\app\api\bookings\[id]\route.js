import { NextResponse } from 'next/server';
import { createServerSupabaseClient, requireAdmin } from '@/lib/auth';

// GET - Get booking by ID
export async function GET(request, { params }) {
  try {
    await requireAdmin();
    
    const { id } = await params;
    const supabase = await createServerSupabaseClient();

    const { data: booking, error } = await supabase
      .from('bookings')
      .select(`
        *,
        customers (
          id,
          first_name,
          last_name,
          email,
          phone,
          city,
          country
        ),
        hotels (
          id,
          name,
          stars,
          cities (
            name,
            countries (
              name
            )
          )
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching booking:', error);
      return NextResponse.json(
        { error: 'Booking not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: booking
    });

  } catch (error) {
    console.error('API error:', error);
    
    if (error.message === 'Authentication required' || error.message === 'Admin privileges required') {
      return NextResponse.json(
        { error: error.message },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT - Update booking details
export async function PUT(request, { params }) {
  try {
    await requireAdmin();
    
    const { id } = await params;
    const updateData = await request.json();
    const supabase = await createServerSupabaseClient();

    // Validate required fields
    const allowedFields = [
      'check_in_date',
      'check_out_date',
      'adults',
      'children',
      'rooms',
      'special_requests',
      'total_amount',
      'currency',
      'admin_notes'
    ];

    const filteredData = {};
    Object.keys(updateData).forEach(key => {
      if (allowedFields.includes(key)) {
        filteredData[key] = updateData[key];
      }
    });

    // Add updated timestamp
    filteredData.updated_at = new Date().toISOString();

    // Update booking
    const { data: updatedBooking, error } = await supabase
      .from('bookings')
      .update(filteredData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating booking:', error);
      return NextResponse.json(
        { error: 'Failed to update booking' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: updatedBooking
    });

  } catch (error) {
    console.error('API error:', error);
    
    if (error.message === 'Authentication required' || error.message === 'Admin privileges required') {
      return NextResponse.json(
        { error: error.message },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE - Cancel booking
export async function DELETE(request, { params }) {
  try {
    await requireAdmin();
    
    const { id } = await params;
    const { reason } = await request.json();
    const supabase = await createServerSupabaseClient();

    // Update booking status to cancelled
    const { data: cancelledBooking, error } = await supabase
      .from('bookings')
      .update({
        status: 'cancelled',
        admin_notes: reason || 'Booking cancelled by admin',
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error cancelling booking:', error);
      return NextResponse.json(
        { error: 'Failed to cancel booking' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: cancelledBooking,
      message: 'Booking cancelled successfully'
    });

  } catch (error) {
    console.error('API error:', error);
    
    if (error.message === 'Authentication required' || error.message === 'Admin privileges required') {
      return NextResponse.json(
        { error: error.message },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
