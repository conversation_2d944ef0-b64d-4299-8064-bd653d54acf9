"use client";
import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import {
  Building2,
  Image as ImageIcon,
  Hotel,
  Settings,
  Users,
  Calendar,
  FileText,
  Menu,
  X,
  Mail,
  BarChart3,
  UserPlus,
  Home,
  LogOut,
  User
} from "lucide-react";
import { SessionProvider, useSession } from "@/components/providers/SessionProvider";

const adminMenuItems = [
  {
    title: "Tableau de Bord",
    href: "/admin",
    icon: <Home className="w-5 h-5" />,
  },
  {
    title: "Réservations",
    href: "/admin/bookings",
    icon: <Calendar className="w-5 h-5" />,
  },
  {
    title: "Clients",
    href: "/admin/customers",
    icon: <Users className="w-5 h-5" />,
  },
  {
    title: "Destinations",
    href: "/admin/destinations",
    icon: <Building2 className="w-5 h-5" />,
  },
  {
    title: "Hôtels",
    href: "/admin/hotels",
    icon: <Hotel className="w-5 h-5" />,
  },
  {
    title: "Campagnes Email",
    href: "/admin/email-campaigns",
    icon: <Mail className="w-5 h-5" />,
  },
  {
    title: "Newsletter",
    href: "/admin/newsletter",
    icon: <UserPlus className="w-5 h-5" />,
  },
  {
    title: "Rapports",
    href: "/admin/reports",
    icon: <BarChart3 className="w-5 h-5" />,
  },
  {
    title: "Page d'Accueil",
    href: "/admin/landing",
    icon: <ImageIcon className="w-5 h-5" />,
  },
  {
    title: "Paramètres",
    href: "/admin/settings",
    icon: <Settings className="w-5 h-5" />,
  },
];

function AdminLayoutContent({ children }) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const pathname = usePathname();
  const { user, profile, signOut, loading } = useSession();

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
      alert('Error signing out. Please try again.');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Sidebar Toggle */}
      <button
        className="fixed top-4 left-4 z-50 p-2 rounded-md bg-white shadow-md lg:hidden"
        onClick={() => setIsSidebarOpen(!isSidebarOpen)}
      >
        {isSidebarOpen ? (
          <X className="w-6 h-6" />
        ) : (
          <Menu className="w-6 h-6" />
        )}
      </button>

      {/* Sidebar */}
      <aside
        className={`fixed top-0 left-0 z-40 h-screen w-64 bg-white shadow-lg transition-transform duration-300 transform ${
          isSidebarOpen ? "translate-x-0" : "-translate-x-full"
        } lg:translate-x-0`}
      >
        <div className="flex flex-col h-full">
          <div className="p-6">
            <Link href="/admin" className="flex items-center space-x-2">
              <Image
                src="/images/logo.svg"
                alt="Bouguerrouche Travel"
                width={32}
                height={32}
                className="h-8 w-auto"
              />
              <span className="text-lg font-semibold text-gray-900">
                Admin Panel
              </span>
            </Link>
          </div>

          <nav className="flex-1 px-4 space-y-1">
            {adminMenuItems.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors duration-200 ${
                    isActive
                      ? "bg-primary text-white"
                      : "text-gray-600 hover:bg-gray-100"
                  }`}
                >
                  {item.icon}
                  <span>{item.title}</span>
                </Link>
              );
            })}
          </nav>

          <div className="p-4 border-t space-y-2">
            {/* User Info */}
            <div className="px-4 py-2 text-sm text-gray-600">
              <div className="flex items-center space-x-2 mb-1">
                <User className="w-4 h-4" />
                <span className="font-medium">{user?.email}</span>
              </div>
              <div className="text-xs text-gray-500">
                Role: {profile?.role || 'Unknown'}
              </div>
            </div>

            {/* Sign Out */}
            <button
              onClick={handleSignOut}
              className="w-full flex items-center space-x-3 px-4 py-3 text-gray-600 hover:bg-red-50 hover:text-red-600 rounded-lg transition-colors duration-200"
            >
              <LogOut className="w-4 h-4" />
              <span>Sign Out</span>
            </button>

            {/* Back to Website */}
            <Link
              href="/"
              className="flex items-center space-x-3 px-4 py-3 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200"
            >
              <span>← Back to Website</span>
            </Link>
          </div>
        </div>
      </aside>

      {/* Main Content */}
      <main
        className={`transition-all duration-300 ${
          isSidebarOpen ? "lg:ml-64" : ""
        }`}
      >
        {children}
      </main>
    </div>
  );
}

export default function AdminLayout({ children }) {
  return (
    <SessionProvider>
      <AdminLayoutContent>{children}</AdminLayoutContent>
    </SessionProvider>
  );
}
