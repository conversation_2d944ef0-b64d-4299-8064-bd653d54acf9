import { NextResponse } from 'next/server';
import { createServerSupabaseClient, requireAdmin } from '@/lib/auth';
import { isValidStatusTransition } from '@/lib/booking-status-client';

export async function POST(request) {
  try {
    // Require admin authentication
    const adminUser = await requireAdmin();
    
    const { bookingId, newStatus, notes } = await request.json();

    if (!bookingId || !newStatus) {
      return NextResponse.json(
        { error: 'Missing required fields: bookingId and newStatus' },
        { status: 400 }
      );
    }

    const supabase = await createServerSupabaseClient();

    // First, get the current booking
    const { data: booking, error: fetchError } = await supabase
      .from('bookings')
      .select('*')
      .eq('id', bookingId)
      .single();

    if (fetchError || !booking) {
      return NextResponse.json(
        { error: 'Booking not found' },
        { status: 404 }
      );
    }

    // Validate status transition
    if (!isValidStatusTransition(booking.status, newStatus)) {
      return NextResponse.json(
        { error: `Invalid status transition from ${booking.status} to ${newStatus}` },
        { status: 400 }
      );
    }

    // Update booking status
    const { data: updatedBooking, error: updateError } = await supabase
      .from('bookings')
      .update({
        status: newStatus,
        updated_at: new Date().toISOString(),
        ...(notes && { admin_notes: notes })
      })
      .eq('id', bookingId)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating booking status:', updateError);
      return NextResponse.json(
        { error: 'Failed to update booking status' },
        { status: 500 }
      );
    }

    // Log the status change
    console.log(`Booking ${bookingId} status changed from ${booking.status} to ${newStatus} by admin ${adminUser.id}`);

    return NextResponse.json({
      success: true,
      data: updatedBooking
    });

  } catch (error) {
    console.error('API error:', error);
    
    if (error.message === 'Authentication required' || error.message === 'Admin privileges required') {
      return NextResponse.json(
        { error: error.message },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
