import { NextResponse } from "next/server";
import { createServerSupabaseClient } from "@/lib/auth";

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);

    // Extract search parameters
    const query = searchParams.get("q") || "";
    const country = searchParams.get("country") || "";
    const city = searchParams.get("city") || "";
    const minPrice = searchParams.get("minPrice")
      ? parseFloat(searchParams.get("minPrice"))
      : null;
    const maxPrice = searchParams.get("maxPrice")
      ? parseFloat(searchParams.get("maxPrice"))
      : null;
    const stars = searchParams.get("stars")
      ? parseInt(searchParams.get("stars"))
      : null;
    const amenities = searchParams.get("amenities")
      ? searchParams.get("amenities").split(",")
      : [];
    const sortBy = searchParams.get("sortBy") || "name";
    const sortOrder = searchParams.get("sortOrder") || "asc";
    const limit = parseInt(searchParams.get("limit")) || 20;
    const offset = parseInt(searchParams.get("offset")) || 0;

    const supabase = await createServerSupabaseClient();

    // Build the query
    let queryBuilder = supabase
      .from("hotels")
      .select(
        `
        *,
        cities (
          id,
          name,
          countries (
            id,
            name
          )
        )
      `
      )
      .eq("is_active", true);

    // Apply text search filter
    if (query) {
      queryBuilder = queryBuilder.or(
        `name.ilike.%${query}%,description.ilike.%${query}%`
      );
    }

    // Apply location filters
    if (country) {
      queryBuilder = queryBuilder.eq("cities.countries.name", country);
    }

    if (city) {
      queryBuilder = queryBuilder.eq("cities.name", city);
    }

    // Apply price filters
    if (minPrice !== null) {
      queryBuilder = queryBuilder.gte("price", minPrice);
    }

    if (maxPrice !== null) {
      queryBuilder = queryBuilder.lte("price", maxPrice);
    }

    // Apply star rating filter
    if (stars !== null) {
      queryBuilder = queryBuilder.eq("stars", stars);
    }

    // Apply amenities filter
    if (amenities.length > 0) {
      // Check if hotel has all specified amenities
      for (const amenity of amenities) {
        queryBuilder = queryBuilder.contains("amenities", [amenity]);
      }
    }

    // Apply sorting
    const validSortFields = ["name", "price", "stars", "created_at"];
    const sortField = validSortFields.includes(sortBy) ? sortBy : "name";
    const ascending = sortOrder === "asc";

    queryBuilder = queryBuilder.order(sortField, { ascending });

    // Apply pagination
    queryBuilder = queryBuilder.range(offset, offset + limit - 1);

    const { data: hotels, error } = await queryBuilder;

    if (error) {
      console.error("Error searching hotels:", error);
      return NextResponse.json(
        { error: "Failed to search hotels" },
        { status: 500 }
      );
    }

    // Get total count for pagination
    let countQuery = supabase
      .from("hotels")
      .select("id", { count: "exact", head: true })
      .eq("is_active", true);

    // Apply the same filters for count
    if (query) {
      countQuery = countQuery.or(
        `name.ilike.%${query}%,description.ilike.%${query}%`
      );
    }

    if (country) {
      countQuery = countQuery.eq("cities.countries.name", country);
    }

    if (city) {
      countQuery = countQuery.eq("cities.name", city);
    }

    if (minPrice !== null) {
      countQuery = countQuery.gte("price", minPrice);
    }

    if (maxPrice !== null) {
      countQuery = countQuery.lte("price", maxPrice);
    }

    if (stars !== null) {
      countQuery = countQuery.eq("stars", stars);
    }

    if (amenities.length > 0) {
      for (const amenity of amenities) {
        countQuery = countQuery.contains("amenities", [amenity]);
      }
    }

    const { count, error: countError } = await countQuery;

    if (countError) {
      console.error("Error counting hotels:", countError);
    }

    return NextResponse.json({
      success: true,
      data: hotels || [],
      pagination: {
        total: count || 0,
        limit,
        offset,
        hasMore: (count || 0) > offset + limit,
      },
      filters: {
        query,
        country,
        city,
        minPrice,
        maxPrice,
        stars,
        amenities,
        sortBy: sortField,
        sortOrder,
      },
    });
  } catch (error) {
    console.error("API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
