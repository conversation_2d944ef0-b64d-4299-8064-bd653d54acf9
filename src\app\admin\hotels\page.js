"use client";
import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import { Plus, Edit2, Trash2, Search } from "lucide-react";
import {
  getHotels,
  getCities,
  createHotel,
  updateHotel,
  deleteHotel,
  toggleTopBookNow,
} from "@/lib/admin-api";
import { HotelModal } from "@/components/admin/DestinationModals";
import { FaStar } from "react-icons/fa6";
import { useSession } from "@/components/providers/SessionProvider";
export default function HotelsManagement() {
  const [hotels, setHotels] = useState([]);
  const [cities, setCities] = useState([]);
  const [isAddingHotel, setIsAddingHotel] = useState(false);
  const [editingHotel, setEditingHotel] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { session, refreshSession } = useSession();

  // Hotel form state
  const [hotelForm, setHotelForm] = useState({
    name: "",
    city_id: "",
    stars: 3,
    description: "",
    price: "",
    currency: "DA",
    image_url: "",
    image_url_1: "",
    image_url_2: "",
    image_url_3: "",
    image_url_4: "",
    discount_percentage: 0,
    offer_text: "",
    highlights: [],
    amenities: [],
    is_featured: false,
    is_active: true,
    // New enhanced fields
    gallery_images: [],
    check_in_time: "15:00",
    check_out_time: "12:00",
    location_url: "",
    address: "",
    phone: "",
    email: "",
    website_url: "",
    special_notices: "",
    property_features: [],
    dining_entertainment: [],
    activities_recreation: [],
    family_facilities: [],
    nearby_attractions: [],
    policies: "",
    cancellation_policy: "",
    payment_methods: [],
  });

  // Load data on component mount
  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      const [hotelsResult, citiesResult] = await Promise.all([
        getHotels(null, false, false), // Get all hotels including inactive
        getCities(),
      ]);

      if (hotelsResult.data) {
        setHotels(hotelsResult.data);
      }
      if (citiesResult.data) {
        setCities(citiesResult.data);
      }
    } catch (error) {
      console.error("Error fetching data:", error);

      // Check if it's an auth error
      if (error.message?.includes("JWT") || error.message?.includes("auth")) {
        setError("Session expired. Please refresh the page and try again.");
        // Attempt to refresh the session
        if (refreshSession) {
          const refreshed = await refreshSession();
          if (refreshed) {
            // Retry fetching data
            setTimeout(() => fetchData(), 1000);
            return;
          }
        }
      } else {
        setError("Error loading data: " + error.message);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleAddHotel = () => {
    resetForm();
    setEditingHotel(null);
    setIsAddingHotel(true);
  };

  const handleEditHotel = (hotel) => {
    setHotelForm({
      name: hotel.name,
      city_id: hotel.city_id,
      stars: hotel.stars,
      description: hotel.description || "",
      price: hotel.price.toString(),
      currency: hotel.currency,
      image_url: hotel.image_url || "",
      image_url_1: hotel.image_url_1 || "",
      image_url_2: hotel.image_url_2 || "",
      image_url_3: hotel.image_url_3 || "",
      image_url_4: hotel.image_url_4 || "",
      discount_percentage: hotel.discount_percentage || 0,
      offer_text: hotel.offer_text || "",
      highlights: hotel.highlights || [],
      amenities: hotel.amenities || [],
      is_featured: hotel.is_featured,
      is_active: hotel.is_active,
      // Enhanced fields
      gallery_images: hotel.gallery_images || [],
      check_in_time: hotel.check_in_time || "15:00",
      check_out_time: hotel.check_out_time || "12:00",
      location_url: hotel.location_url || "",
      address: hotel.address || "",
      phone: hotel.phone || "",
      email: hotel.email || "",
      website_url: hotel.website_url || "",
      special_notices: hotel.special_notices || "",
      property_features: hotel.property_features || [],
      dining_entertainment: hotel.dining_entertainment || [],
      activities_recreation: hotel.activities_recreation || [],
      family_facilities: hotel.family_facilities || [],
      nearby_attractions: hotel.nearby_attractions || [],
      policies: hotel.policies || "",
      cancellation_policy: hotel.cancellation_policy || "",
      payment_methods: hotel.payment_methods || [],
    });
    setEditingHotel(hotel);
    setIsAddingHotel(true);
  };

  const handleDeleteHotel = async (hotelId) => {
    if (confirm("Are you sure you want to delete this hotel?")) {
      try {
        const { error } = await deleteHotel(hotelId);
        if (error) throw error;
        setHotels(hotels.filter((hotel) => hotel.id !== hotelId));
      } catch (error) {
        console.error("Error deleting hotel:", error);
        alert("Error deleting hotel");
      }
    }
  };

  const handleSubmitHotel = async (e) => {
    e.preventDefault();
    try {
      const hotelData = {
        ...hotelForm,
        price: parseFloat(hotelForm.price),
        latitude: hotelForm.latitude ? parseFloat(hotelForm.latitude) : null,
        longitude: hotelForm.longitude ? parseFloat(hotelForm.longitude) : null,
        highlights: hotelForm.highlights.filter((h) => h.trim() !== ""),
        amenities: hotelForm.amenities.filter((a) => a.trim() !== ""),
        gallery_images: hotelForm.gallery_images.filter(
          (img) => img.trim() !== ""
        ),
        property_features: hotelForm.property_features.filter(
          (f) => f.trim() !== ""
        ),
        dining_entertainment: hotelForm.dining_entertainment.filter(
          (d) => d.trim() !== ""
        ),
        activities_recreation: hotelForm.activities_recreation.filter(
          (a) => a.trim() !== ""
        ),
        family_facilities: hotelForm.family_facilities.filter(
          (f) => f.trim() !== ""
        ),
        nearby_attractions: hotelForm.nearby_attractions.filter(
          (n) => n.trim() !== ""
        ),
        payment_methods: hotelForm.payment_methods.filter(
          (p) => p.trim() !== ""
        ),
      };

      if (editingHotel) {
        const { data, error } = await updateHotel(editingHotel.id, hotelData);
        if (error) throw error;
        setHotels(hotels.map((h) => (h.id === editingHotel.id ? data : h)));
      } else {
        const { data, error } = await createHotel(hotelData);
        if (error) throw error;
        setHotels([...hotels, data]);
      }
      setIsAddingHotel(false);
      resetForm();
    } catch (error) {
      console.error("Error saving hotel:", error);
      alert("Error saving hotel");
    }
  };

  const resetForm = () => {
    setHotelForm({
      name: "",
      city_id: "",
      stars: 3,
      description: "",
      price: "",
      currency: "DA",
      image_url: "",
      image_url_1: "",
      image_url_2: "",
      image_url_3: "",
      image_url_4: "",
      discount_percentage: 0,
      offer_text: "",
      highlights: [],
      amenities: [],
      is_featured: false,
      is_active: true,
      // Enhanced fields
      gallery_images: [],
      check_in_time: "15:00",
      check_out_time: "12:00",
      location_url: "",
      address: "",
      phone: "",
      email: "",
      website_url: "",
      special_notices: "",
      property_features: [],
      dining_entertainment: [],
      activities_recreation: [],
      family_facilities: [],
      nearby_attractions: [],
      policies: "",
      cancellation_policy: "",
      payment_methods: [],
    });
    setEditingHotel(null);
  };

  // Helper functions for highlights and amenities
  const addHighlight = (type) => {
    setHotelForm({
      ...hotelForm,
      highlights: [...hotelForm.highlights, ""],
    });
  };

  const updateHighlight = (type, index, value) => {
    const newHighlights = [...hotelForm.highlights];
    newHighlights[index] = value;
    setHotelForm({
      ...hotelForm,
      highlights: newHighlights,
    });
  };

  const removeHighlight = (type, index) => {
    setHotelForm({
      ...hotelForm,
      highlights: hotelForm.highlights.filter((_, i) => i !== index),
    });
  };

  const addAmenity = (type) => {
    setHotelForm({
      ...hotelForm,
      amenities: [...hotelForm.amenities, ""],
    });
  };

  const updateAmenity = (type, index, value) => {
    const newAmenities = [...hotelForm.amenities];
    newAmenities[index] = value;
    setHotelForm({
      ...hotelForm,
      amenities: newAmenities,
    });
  };

  const removeAmenity = (type, index) => {
    setHotelForm({
      ...hotelForm,
      amenities: hotelForm.amenities.filter((_, i) => i !== index),
    });
  };

  const filteredHotels = hotels.filter(
    (hotel) =>
      hotel.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (hotel.cities?.name &&
        hotel.cities.name.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="container-custom">
          <div className="flex justify-center items-center h-64">
            <div className="text-lg text-gray-600">Loading hotels...</div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="container-custom">
          <div className="flex flex-col justify-center items-center h-64">
            <div className="text-lg text-red-600 mb-4">{error}</div>
            <Button
              onClick={fetchData}
              className="bg-blue-600 hover:bg-blue-700"
            >
              Retry
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container-custom">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Hotels Management
          </h1>
          <Button
            variant="primary"
            onClick={handleAddHotel}
            icon={<Plus className="w-4 h-4" />}
          >
            Add New Hotel
          </Button>
        </div>

        <div className="mb-6">
          <Input
            type="text"
            placeholder="Search hotels..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            icon={<Search className="w-4 h-4" />}
          />
        </div>

        <div className="grid gap-6">
          {filteredHotels.map((hotel) => (
            <Card key={hotel.id}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="text-xl font-semibold text-gray-900">
                        {hotel.name}
                      </h3>
                      <span className="text-accent">
                        {"★".repeat(hotel.stars)}
                      </span>
                      {!hotel.is_active && (
                        <span className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded">
                          Inactive
                        </span>
                      )}
                      {hotel.is_featured && (
                        <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                          Featured
                        </span>
                      )}
                    </div>
                    <p className="text-gray-600 mb-2">
                      {hotel.cities?.name}, {hotel.cities?.countries?.name}
                    </p>
                    <p className="text-sm text-gray-500 mb-4">
                      {hotel.offer_text}
                    </p>
                    <div className="flex items-center gap-4">
                      <span className="text-lg font-bold text-primary">
                        {hotel.price.toLocaleString()} {hotel.currency}
                      </span>
                      {hotel.discount_percentage > 0 && (
                        <span className="text-sm text-red-500">
                          -{hotel.discount_percentage}%
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditHotel(hotel)}
                      icon={<Edit2 className="w-4 h-4" />}
                    >
                      Edit
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteHotel(hotel.id)}
                      icon={<Trash2 className="w-4 h-4" />}
                    >
                      Delete
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={async () => {
                        try {
                          console.log(
                            "Toggling top_book_now for",
                            hotel.id,
                            "Current:",
                            hotel.top_book_now
                          );
                          await toggleTopBookNow(hotel.id, hotel.top_book_now);
                          console.log("Toggled. Now refreshing...");
                          await fetchData();
                        } catch (err) {
                          console.error("Error toggling top_book_now:", err);
                        }
                      }}
                      icon={
                        <FaStar
                          className={
                            hotel.top_book_now
                              ? "text-yellow-400"
                              : "text-gray-400"
                          }
                        />
                      }
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Hotel Modal */}
        <HotelModal
          isOpen={isAddingHotel}
          onClose={() => {
            setIsAddingHotel(false);
            resetForm();
          }}
          onSubmit={handleSubmitHotel}
          form={hotelForm}
          setForm={setHotelForm}
          cities={cities}
          isEditing={!!editingHotel}
          onAddHighlight={addHighlight}
          onUpdateHighlight={updateHighlight}
          onRemoveHighlight={removeHighlight}
          onAddAmenity={addAmenity}
          onUpdateAmenity={updateAmenity}
          onRemoveAmenity={removeAmenity}
        />
      </div>
    </div>
  );
}
