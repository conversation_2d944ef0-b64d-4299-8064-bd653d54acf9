"use client";
import { useState, useEffect } from "react";
import Header from "@/components/header/Header.jsx";
import Footer from "@/components/footer/Footer.jsx";
import Image from "next/image";
import Link from "next/link";
import Button from "@/components/ui/Button";
import HotelSearchFilters from "@/components/destinations/HotelSearchFilters";
import HotelGrid from "@/components/destinations/HotelGrid";
import { useToast } from "@/components/ui/Toast";

export default function Destinations() {
  const [hotels, setHotels] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({});
  const [totalResults, setTotalResults] = useState(0);
  const { toast } = useToast();

  // Load initial hotels on mount
  useEffect(() => {
    handleSearch('', {});
  }, []);

  const handleSearch = async (query, searchFilters) => {
    setLoading(true);
    setError(null);
    setSearchQuery(query);
    setFilters(searchFilters);

    try {
      const params = new URLSearchParams();

      if (query) params.append('query', query);
      if (searchFilters.country) params.append('country', searchFilters.country);
      if (searchFilters.city) params.append('city', searchFilters.city);
      if (searchFilters.minPrice) params.append('minPrice', searchFilters.minPrice);
      if (searchFilters.maxPrice) params.append('maxPrice', searchFilters.maxPrice);
      if (searchFilters.stars) params.append('stars', searchFilters.stars);
      if (searchFilters.amenities && searchFilters.amenities.length > 0) {
        params.append('amenities', searchFilters.amenities.join(','));
      }
      if (searchFilters.featured) params.append('featured', 'true');
      if (searchFilters.sortBy) params.append('sortBy', searchFilters.sortBy);
      if (searchFilters.sortOrder) params.append('sortOrder', searchFilters.sortOrder);

      const response = await fetch(`/api/hotels/search?${params.toString()}`);

      if (!response.ok) {
        throw new Error('Erreur lors de la recherche des hôtels');
      }

      const data = await response.json();
      setHotels(data.data || []);
      setTotalResults(data.total || 0);

    } catch (err) {
      console.error('Error searching hotels:', err);
      setError(err.message);
      setHotels([]);
      setTotalResults(0);
    } finally {
      setLoading(false);
    }
  };

  const handleFiltersChange = (newFilters) => {
    // Debounce filter changes to avoid too many API calls
    const timeoutId = setTimeout(() => {
      handleSearch(searchQuery, newFilters);
    }, 300);

    return () => clearTimeout(timeoutId);
  };


  return (
    <>
      <Header />
      <main className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <section className="relative h-[60vh] md:h-[80vh] min-h-[400px] w-full overflow-hidden flex items-center justify-center">
          <Image
            src="/images/landingpagebackground.svg"
            alt="Destinations background"
            fill
            className="object-cover z-0"
            priority
            sizes="100vw"
          />
          <div className="relative z-10 text-center text-white px-4">
            <h1 className="text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-extrabold mb-2 leading-tight tracking-tight font-poppins">
              Nos Destinations
            </h1>
            <hr className="border-t border-white w-full md:w-[90%] mb-6 mt-2 opacity-80 mx-auto" />
            <p className="text-lg sm:text-xl text-gray-200 mb-8 sm:mb-10 leading-relaxed max-w-3xl mx-auto font-poppins">
              Découvrez nos destinations exceptionnelles à travers le monde,
              avec la Tunisie en vedette pour cette saison
            </p>
          </div>
        </section>

        {/* Hotel Search and Display */}
        <section className="w-full bg-white section-padding">
          <div className="container-custom">
            <div className="text-center mb-10">
              <h2 className="text-heading text-3xl md:text-4xl text-center mb-4 tracking-tight text-gray-900">
                EXPLOREZ NOS DESTINATIONS
              </h2>
              <p className="text-body max-w-2xl mx-auto mb-6">
                Découvrez nos hôtels exceptionnels à travers le monde
              </p>
              {totalResults > 0 && (
                <p className="text-sm text-gray-600">
                  {totalResults} hôtel{totalResults > 1 ? 's' : ''} trouvé{totalResults > 1 ? 's' : ''}
                  {searchQuery && ` pour "${searchQuery}"`}
                </p>
              )}
            </div>

            {/* Search and Filters */}
            <div className="mb-8">
              <HotelSearchFilters
                onSearch={handleSearch}
                onFiltersChange={handleFiltersChange}
                loading={loading}
                initialFilters={filters}
              />
            </div>

            {/* Hotels Grid */}
            <HotelGrid
              hotels={hotels}
              loading={loading}
              error={error}
            />
          </div>
        </section>

        {/* Call to Action */}
        <section className="w-full bg-white section-padding">
          <div className="container-custom text-center">
            <h2 className="text-heading text-3xl md:text-4xl text-center mb-10 tracking-tight text-gray-900">
              PRÊT À PARTIR À L&apos;AVENTURE ?
            </h2>
            <p className="text-body mb-8 max-w-2xl mx-auto">
              Contactez-nous pour planifier votre voyage sur mesure en Tunisie
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/contact">
                <Button
                  variant="secondary"
                  size="lg"
                  className="rounded-full text-lg"
                >
                  Nous Contacter
                </Button>
              </Link>
              <Link href="/">
                <Button
                  variant="outline"
                  size="lg"
                  className="rounded-full text-lg"
                >
                  Retour à l&apos;accueil
                </Button>
              </Link>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
