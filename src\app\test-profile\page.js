"use client";

import { useState } from 'react';
import { createBrowserClient } from '@supabase/ssr';

export default function TestProfilePage() {
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);

  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  );

  const testProfileLookup = async () => {
    setLoading(true);
    try {
      // Test 1: Get all profiles
      const { data: allProfiles, error: allError } = await supabase
        .from('profiles')
        .select('*');

      console.log('All profiles:', allProfiles, 'Error:', allError);

      // Test 2: Get specific profile
      const { data: specificProfile, error: specificError } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', '3bc781a6-c77a-4cbc-807d-86340a6e45c6')
        .single();

      console.log('Specific profile:', specificProfile, 'Error:', specificError);

      // Test 3: Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      console.log('Current user:', user, 'Error:', userError);

      setResult({
        allProfiles: { data: allProfiles, error: allError },
        specificProfile: { data: specificProfile, error: specificError },
        currentUser: { data: user, error: userError }
      });

    } catch (error) {
      console.error('Test error:', error);
      setResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Profile Test Page
        </h1>

        <div className="space-y-6">
          <button
            onClick={testProfileLookup}
            disabled={loading}
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:bg-blue-400"
          >
            {loading ? 'Testing...' : 'Test Profile Lookup'}
          </button>

          {result && (
            <div className="bg-white p-6 rounded-lg shadow">
              <h2 className="text-xl font-semibold mb-4">Test Results:</h2>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}

          <div className="bg-yellow-50 p-4 rounded-lg">
            <h3 className="font-semibold text-yellow-800 mb-2">Instructions:</h3>
            <ol className="list-decimal list-inside text-yellow-700 space-y-1">
              <li>Click "Test Profile Lookup" to test database connection</li>
              <li>Check the browser console (F12) for detailed logs</li>
              <li>Check the results displayed below</li>
              <li>This will help debug the authentication issue</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
}
