import { NextResponse } from 'next/server';
import { createServerSupabaseClient, requireAdmin } from '@/lib/auth';
import { PAYMENT_STATUSES } from '@/lib/booking-status-client';

export async function POST(request) {
  try {
    // Require admin authentication
    const adminUser = await requireAdmin();
    
    const { bookingId, newPaymentStatus, notes } = await request.json();

    if (!bookingId || !newPaymentStatus) {
      return NextResponse.json(
        { error: 'Missing required fields: bookingId and newPaymentStatus' },
        { status: 400 }
      );
    }

    // Validate payment status
    if (!Object.values(PAYMENT_STATUSES).includes(newPaymentStatus)) {
      return NextResponse.json(
        { error: `Invalid payment status: ${newPaymentStatus}` },
        { status: 400 }
      );
    }

    const supabase = await createServerSupabaseClient();

    // Update payment status
    const { data: updatedBooking, error: updateError } = await supabase
      .from('bookings')
      .update({
        payment_status: newPaymentStatus,
        updated_at: new Date().toISOString(),
        ...(notes && { admin_notes: notes })
      })
      .eq('id', bookingId)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating payment status:', updateError);
      return NextResponse.json(
        { error: 'Failed to update payment status' },
        { status: 500 }
      );
    }

    // Log the payment status change
    console.log(`Booking ${bookingId} payment status changed to ${newPaymentStatus} by admin ${adminUser.id}`);

    return NextResponse.json({
      success: true,
      data: updatedBooking
    });

  } catch (error) {
    console.error('API error:', error);
    
    if (error.message === 'Authentication required' || error.message === 'Admin privileges required') {
      return NextResponse.json(
        { error: error.message },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
