"use client";
import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/Card";
import {
  Building2,
  Image as ImageIcon,
  Hotel,
  Settings,
  Users,
  Calendar,
  FileText,
  Mail,
  TrendingUp,
  Globe,
  CheckCircle,
  Clock,
  AlertCircle
} from "lucide-react";
import Link from "next/link";
import { getDashboardStats } from "@/lib/database";

const adminMenuItems = [
  {
    title: "Réservations",
    description: "Gérer les réservations clients",
    icon: <Calendar className="w-6 h-6" />,
    href: "/admin/bookings",
  },
  {
    title: "Clients",
    description: "Gérer les informations clients",
    icon: <Users className="w-6 h-6" />,
    href: "/admin/customers",
  },
  {
    title: "Destinations",
    description: "Gérer les destinations de voyage",
    icon: <Building2 className="w-6 h-6" />,
    href: "/admin/destinations",
  },
  {
    title: "Hôtels",
    description: "<PERSON><PERSON><PERSON> les hôtels et hébergements",
    icon: <Hotel className="w-6 h-6" />,
    href: "/admin/hotels",
  },
  {
    title: "Campagnes Email",
    description: "Envoyer des offres et newsletters",
    icon: <Mail className="w-6 h-6" />,
    href: "/admin/email-campaigns",
  },
  {
    title: "Newsletter",
    description: "Gérer les abonnés newsletter",
    icon: <Users className="w-6 h-6" />,
    href: "/admin/newsletter",
  },
  {
    title: "Rapports",
    description: "Voir les rapports et analyses",
    icon: <FileText className="w-6 h-6" />,
    href: "/admin/reports",
  },
  {
    title: "Page d'Accueil",
    description: "Modifier le contenu de la page d'accueil",
    icon: <ImageIcon className="w-6 h-6" />,
    href: "/admin/landing",
  },
  {
    title: "Paramètres",
    description: "Configurer les paramètres du site",
    icon: <Settings className="w-6 h-6" />,
    href: "/admin/settings",
  },
];

export default function AdminDashboard() {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    setLoading(true);
    try {
      const { data, error } = await getDashboardStats();
      if (error) {
        setError(`Erreur lors du chargement des statistiques: ${error.message}`);
      } else {
        setStats(data);
      }
    } catch (error) {
      setError(`Erreur de connexion: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const getStatColor = (type) => {
    const colors = {
      bookings: "bg-blue-500",
      customers: "bg-green-500",
      hotels: "bg-purple-500",
      revenue: "bg-orange-500",
      pending: "bg-yellow-500",
      confirmed: "bg-emerald-500"
    };
    return colors[type] || "bg-gray-500";
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container-custom py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Tableau de Bord Admin
          </h1>
          <div className="text-sm text-gray-500">
            Dernière mise à jour: {new Date().toLocaleString('fr-FR')}
          </div>
        </div>

        {/* Statistics Overview */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : error ? (
          <Card className="mb-8 border-red-200 bg-red-50">
            <CardContent className="p-6">
              <div className="flex items-center gap-3 text-red-700">
                <AlertCircle className="w-5 h-5" />
                <span>{error}</span>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Réservations</p>
                    <p className="text-2xl font-bold text-gray-900">{stats?.totalBookings || 0}</p>
                  </div>
                  <div className="p-3 bg-blue-100 rounded-full">
                    <Calendar className="w-6 h-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Clients Actifs</p>
                    <p className="text-2xl font-bold text-gray-900">{stats?.totalCustomers || 0}</p>
                  </div>
                  <div className="p-3 bg-green-100 rounded-full">
                    <Users className="w-6 h-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Hôtels Actifs</p>
                    <p className="text-2xl font-bold text-gray-900">{stats?.totalHotels || 0}</p>
                  </div>
                  <div className="p-3 bg-purple-100 rounded-full">
                    <Hotel className="w-6 h-6 text-purple-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Revenus (30j)</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {(stats?.totalRevenue || 0).toLocaleString()} DA
                    </p>
                  </div>
                  <div className="p-3 bg-orange-100 rounded-full">
                    <TrendingUp className="w-6 h-6 text-orange-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Quick Status Overview */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            <Card className="border-l-4 border-yellow-500">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <Clock className="w-5 h-5 text-yellow-600" />
                  <div>
                    <p className="text-sm text-gray-600">En Attente</p>
                    <p className="text-lg font-semibold">{stats.pendingBookings}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-green-500">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <div>
                    <p className="text-sm text-gray-600">Confirmées</p>
                    <p className="text-lg font-semibold">{stats.confirmedBookings}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-blue-500">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <Globe className="w-5 h-5 text-blue-600" />
                  <div>
                    <p className="text-sm text-gray-600">Pays</p>
                    <p className="text-lg font-semibold">{stats.totalCountries}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-purple-500">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <Mail className="w-5 h-5 text-purple-600" />
                  <div>
                    <p className="text-sm text-gray-600">Newsletter</p>
                    <p className="text-lg font-semibold">{stats.newsletterSubscribers}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Recent Bookings */}
        {stats?.recentBookings && stats.recentBookings.length > 0 && (
          <Card className="mb-8">
            <CardContent className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Réservations Récentes</h2>
              <div className="space-y-3">
                {stats.recentBookings.map((booking) => (
                  <div key={booking.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium">
                        {booking.customers?.first_name} {booking.customers?.last_name}
                      </p>
                      <p className="text-sm text-gray-600">
                        {booking.hotels?.name} - {booking.hotels?.cities?.name}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-500">
                        {new Date(booking.created_at).toLocaleDateString('fr-FR')}
                      </p>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        booking.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                        booking.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {booking.status === 'confirmed' ? 'Confirmé' :
                         booking.status === 'pending' ? 'En attente' : booking.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Admin Menu */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Gestion</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {adminMenuItems.map((item, index) => (
              <Link key={index} href={item.href}>
                <Card className="h-full hover:shadow-lg transition-shadow duration-200">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="p-3 bg-primary/10 rounded-lg text-primary">
                        {item.icon}
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-1">
                          {item.title}
                        </h3>
                        <p className="text-sm text-gray-600">
                          {item.description}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
