import { createServerClient } from "@supabase/ssr";
import { NextResponse } from "next/server";

export async function middleware(request) {
  try {
    // Create a Supabase client configured to use cookies
    let response = NextResponse.next({
      request: {
        headers: request.headers,
      },
    });

    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          get(name) {
            return request.cookies.get(name)?.value;
          },
          set(name, value, options) {
            request.cookies.set({
              name,
              value,
              ...options,
            });
            response = NextResponse.next({
              request: {
                headers: request.headers,
              },
            });
            response.cookies.set({
              name,
              value,
              ...options,
            });
          },
          remove(name, options) {
            request.cookies.set({
              name,
              value: "",
              ...options,
            });
            response = NextResponse.next({
              request: {
                headers: request.headers,
              },
            });
            response.cookies.set({
              name,
              value: "",
              ...options,
            });
          },
        },
      }
    );

    // Check if the route is an admin route
    if (request.nextUrl.pathname.startsWith("/admin")) {
      // Get the current session
      const {
        data: { session },
        error: sessionError,
      } = await supabase.auth.getSession();

      // If no session, redirect to signin
      if (!session || sessionError) {
        const redirectUrl = new URL("/signin", request.url);
        redirectUrl.searchParams.set("redirectTo", request.nextUrl.pathname);
        return NextResponse.redirect(redirectUrl);
      }

      // Check if user has admin role
      const { data: profile, error: profileError } = await supabase
        .from("profiles")
        .select("role")
        .eq("user_id", session.user.id)
        .single();

      // If no profile or not admin, redirect to signin with error
      if (profileError || !profile || profile.role !== "admin") {
        const redirectUrl = new URL("/signin", request.url);
        redirectUrl.searchParams.set("error", "access_denied");
        redirectUrl.searchParams.set("message", "Admin access required");
        return NextResponse.redirect(redirectUrl);
      }

      // Refresh the session if it's close to expiring (within 30 minutes)
      if (session.expires_at) {
        const expiresAt = new Date(session.expires_at * 1000);
        const now = new Date();
        const thirtyMinutes = 30 * 60 * 1000;

        if (expiresAt.getTime() - now.getTime() < thirtyMinutes) {
          console.log(
            "Middleware: Refreshing session (expires in",
            Math.round((expiresAt.getTime() - now.getTime()) / 60000),
            "minutes)"
          );
          try {
            const { data: refreshData, error: refreshError } =
              await supabase.auth.refreshSession();
            if (refreshError) {
              console.error(
                "Middleware: Failed to refresh session:",
                refreshError
              );
              // Redirect to signin if refresh fails
              const redirectUrl = new URL("/signin", request.url);
              redirectUrl.searchParams.set("error", "session_expired");
              redirectUrl.searchParams.set(
                "message",
                "Session expired, please sign in again"
              );
              return NextResponse.redirect(redirectUrl);
            } else {
              console.log("Middleware: Session refreshed successfully");
            }
          } catch (error) {
            console.error("Middleware: Error refreshing session:", error);
          }
        }
      }
    }

    return response;
  } catch (error) {
    console.error("Middleware error:", error);
    // On error, redirect to signin for admin routes
    if (request.nextUrl.pathname.startsWith("/admin")) {
      const redirectUrl = new URL("/signin", request.url);
      redirectUrl.searchParams.set("error", "auth_error");
      return NextResponse.redirect(redirectUrl);
    }
    return NextResponse.next();
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!api|_next/static|_next/image|favicon.ico|public).*)",
  ],
};
