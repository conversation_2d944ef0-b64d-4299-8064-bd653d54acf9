import { NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/auth';

export async function GET() {
  try {
    const supabase = await createServerSupabaseClient();
    
    const { data: countries, error } = await supabase
      .from('countries')
      .select(`
        id,
        name,
        code,
        currency,
        is_featured,
        cities (
          id,
          name,
          hotels!inner (
            id
          )
        )
      `)
      .eq('cities.hotels.is_active', true)
      .order('name');

    if (error) {
      console.error('Error fetching countries:', error);
      return NextResponse.json(
        { error: 'Failed to fetch countries' },
        { status: 500 }
      );
    }

    // Filter countries that have cities with active hotels
    const countriesWithHotels = countries.filter(country => 
      country.cities && country.cities.length > 0
    );

    return NextResponse.json({
      success: true,
      data: countriesWithHotels
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
