import{createClient}from"@supabase/supabase-js";const supabaseUrl=process.env.NEXT_PUBLIC_SUPABASE_URL||process.env.supabaseUrl,supabaseKey=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY||process.env.supabaseKey;if(!supabaseUrl)throw new Error("Missing Supabase URL. Please set NEXT_PUBLIC_SUPABASE_URL in your .env file.\nYou can find this in your Supabase project settings under API.");if(!supabaseKey)throw new Error("Missing Supabase Anon Key. Please set NEXT_PUBLIC_SUPABASE_ANON_KEY in your .env file.\nYou can find this in your Supabase project settings under API.");const supabase=createClient(supabaseUrl,supabaseKey,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},realtime:{params:{eventsPerSecond:10}}});export default supabase;export{supabaseUrl,supabaseKey};