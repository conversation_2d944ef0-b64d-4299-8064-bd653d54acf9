import supabase from '@/Config/supabase';

/**
 * Guest Booking Service
 * Handles guest-based bookings without requiring user authentication
 */

/**
 * Find or create a customer based on email
 * This ensures we don't create duplicate customers for repeat guests
 */
export const findOrCreateCustomer = async (customerData) => {
  try {
    // First, try to find existing customer by email
    const { data: existingCustomer, error: findError } = await supabase
      .from('customers')
      .select('*')
      .eq('email', customerData.email)
      .single();

    if (findError && findError.code !== 'PGRST116') {
      // PGRST116 is "not found" error, which is expected for new customers
      throw findError;
    }

    if (existingCustomer) {
      // Customer exists, update their information with latest data
      const { data: updatedCustomer, error: updateError } = await supabase
        .from('customers')
        .update({
          first_name: customerData.first_name || existingCustomer.first_name,
          last_name: customerData.last_name || existingCustomer.last_name,
          phone: customerData.phone || existingCustomer.phone,
          address: customerData.address || existingCustomer.address,
          city: customerData.city || existingCustomer.city,
          country: customerData.country || existingCustomer.country,
          is_newsletter_subscribed: customerData.is_newsletter_subscribed ?? existingCustomer.is_newsletter_subscribed,
          total_bookings: existingCustomer.total_bookings + 1
        })
        .eq('id', existingCustomer.id)
        .select()
        .single();

      if (updateError) throw updateError;
      return { data: updatedCustomer, error: null, isNewCustomer: false };
    } else {
      // Customer doesn't exist, create new one
      const { data: newCustomer, error: createError } = await supabase
        .from('customers')
        .insert([{
          ...customerData,
          is_guest: true,
          total_bookings: 1
        }])
        .select()
        .single();

      if (createError) throw createError;
      return { data: newCustomer, error: null, isNewCustomer: true };
    }
  } catch (error) {
    console.error('Error finding or creating customer:', error);
    return { data: null, error, isNewCustomer: false };
  }
};

/**
 * Create a guest booking
 * This function handles the entire guest booking process:
 * 1. Find or create customer
 * 2. Create booking record
 * 3. Generate booking reference
 */
export const createGuestBooking = async (bookingData) => {
  try {
    // Extract customer data from booking data
    const customerData = {
      email: bookingData.customer_email,
      first_name: bookingData.customer_first_name,
      last_name: bookingData.customer_last_name,
      phone: bookingData.customer_phone,
      address: bookingData.customer_address,
      city: bookingData.customer_city,
      country: bookingData.customer_country,
      is_newsletter_subscribed: bookingData.newsletter_subscription || false
    };

    // Find or create customer
    const { data: customer, error: customerError, isNewCustomer } = await findOrCreateCustomer(customerData);
    if (customerError) {
      throw new Error(`Customer creation failed: ${customerError.message}`);
    }

    // Generate unique booking reference
    const bookingReference = `BT${Date.now()}${Math.random().toString(36).substr(2, 4).toUpperCase()}`;

    // Prepare booking data
    const booking = {
      customer_id: customer.id,
      hotel_id: bookingData.hotel_id,
      check_in_date: bookingData.check_in_date,
      check_out_date: bookingData.check_out_date,
      adults: bookingData.adults || 1,
      children: bookingData.children || 0,
      rooms: bookingData.rooms || 1,
      total_amount: bookingData.total_amount,
      currency: bookingData.currency || 'DA',
      status: 'pending',
      booking_reference: bookingReference,
      special_requests: bookingData.special_requests || null,
      payment_status: 'pending'
    };

    // Create booking
    const { data: newBooking, error: bookingError } = await supabase
      .from('bookings')
      .insert([booking])
      .select(`
        *,
        customers (
          first_name,
          last_name,
          email,
          phone
        ),
        hotels (
          name,
          stars,
          cities (
            name,
            countries (
              name
            )
          )
        )
      `)
      .single();

    if (bookingError) {
      throw new Error(`Booking creation failed: ${bookingError.message}`);
    }

    return {
      data: {
        booking: newBooking,
        customer: customer,
        isNewCustomer: isNewCustomer
      },
      error: null
    };

  } catch (error) {
    console.error('Error creating guest booking:', error);
    return { data: null, error };
  }
};

/**
 * Get booking by reference number
 * Allows guests to look up their booking without authentication
 */
export const getBookingByReference = async (bookingReference) => {
  try {
    const { data, error } = await supabase
      .from('bookings')
      .select(`
        *,
        customers (
          first_name,
          last_name,
          email,
          phone
        ),
        hotels (
          name,
          stars,
          image_url,
          cities (
            name,
            region,
            countries (
              name
            )
          )
        )
      `)
      .eq('booking_reference', bookingReference.toUpperCase())
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error fetching booking by reference:', error);
    return { data: null, error };
  }
};

/**
 * Update booking status (for admin use)
 */
export const updateBookingStatus = async (bookingId, status, paymentStatus = null) => {
  try {
    const updateData = { status };
    if (paymentStatus) {
      updateData.payment_status = paymentStatus;
    }

    const { data, error } = await supabase
      .from('bookings')
      .update(updateData)
      .eq('id', bookingId)
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error updating booking status:', error);
    return { data: null, error };
  }
};

/**
 * Get customer booking history by email
 * Useful for customer service and repeat guest identification
 */
export const getCustomerBookingHistory = async (email) => {
  try {
    const { data, error } = await supabase
      .from('bookings')
      .select(`
        *,
        hotels (
          name,
          stars,
          cities (
            name,
            countries (
              name
            )
          )
        )
      `)
      .eq('customers.email', email)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error fetching customer booking history:', error);
    return { data: null, error };
  }
};

/**
 * Send booking confirmation email (placeholder for email service integration)
 */
export const sendBookingConfirmation = async (bookingData) => {
  // This would integrate with your email service (SendGrid, Mailgun, etc.)
  console.log('Sending booking confirmation email:', {
    to: bookingData.customer.email,
    bookingReference: bookingData.booking.booking_reference,
    hotelName: bookingData.booking.hotels?.name,
    checkIn: bookingData.booking.check_in_date,
    checkOut: bookingData.booking.check_out_date
  });
  
  // Return success for now
  return { success: true };
};
