"use client";
import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import {
  Send,
  Users,
  Mail,
  Plus,
  Eye,
  Edit,
  Trash2,
  Calendar,
  BarChart3,
  Clock,
  CheckCircle,
  X
} from "lucide-react";
import {
  getEmailCampaigns,
  createEmailCampaign,
  updateEmailCampaign,
  deleteEmailCampaign,
  getNewsletterSubscribers
} from "@/lib/database";

export default function EmailCampaigns() {
  const [campaigns, setCampaigns] = useState([]);
  const [subscribers, setSubscribers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('campaigns'); // campaigns, create, subscribers
  const [editingCampaign, setEditingCampaign] = useState(null);

  const [campaignForm, setCampaignForm] = useState({
    name: "",
    subject: "",
    content: "",
    status: "draft",
    scheduled_at: null
  });

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      const [campaignsRes, subscribersRes] = await Promise.all([
        getEmailCampaigns(),
        getNewsletterSubscribers(false) // Get all subscribers
      ]);

      if (campaignsRes.error) {
        setError(`Erreur lors du chargement des campagnes: ${campaignsRes.error.message}`);
      } else {
        setCampaigns(campaignsRes.data || []);
      }

      if (subscribersRes.error) {
        setError(`Erreur lors du chargement des abonnés: ${subscribersRes.error.message}`);
      } else {
        setSubscribers(subscribersRes.data || []);
      }
    } catch (error) {
      setError(`Erreur de connexion: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setCampaignForm({
      name: "",
      subject: "",
      content: "",
      status: "draft",
      scheduled_at: null
    });
    setEditingCampaign(null);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      if (editingCampaign) {
        const { data, error } = await updateEmailCampaign(editingCampaign.id, campaignForm);
        if (error) throw error;
        setCampaigns(campaigns.map(c => c.id === editingCampaign.id ? data : c));
      } else {
        const { data, error } = await createEmailCampaign(campaignForm);
        if (error) throw error;
        setCampaigns([data, ...campaigns]);
      }
      setActiveTab('campaigns');
      resetForm();
    } catch (error) {
      console.error('Error saving campaign:', error);
      alert('Erreur lors de la sauvegarde de la campagne');
    }
  };

  const handleEdit = (campaign) => {
    setCampaignForm({
      name: campaign.name,
      subject: campaign.subject,
      content: campaign.content,
      status: campaign.status,
      scheduled_at: campaign.scheduled_at
    });
    setEditingCampaign(campaign);
    setActiveTab('create');
  };

  const handleDelete = async (campaignId) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cette campagne ?')) return;

    try {
      const { error } = await deleteEmailCampaign(campaignId);
      if (error) throw error;
      setCampaigns(campaigns.filter(c => c.id !== campaignId));
    } catch (error) {
      console.error('Error deleting campaign:', error);
      alert('Erreur lors de la suppression de la campagne');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'sent':
        return 'bg-green-100 text-green-800';
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'sent':
        return 'Envoyé';
      case 'scheduled':
        return 'Programmé';
      case 'draft':
        return 'Brouillon';
      case 'cancelled':
        return 'Annulé';
      default:
        return status;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-lg text-gray-600">Chargement...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container-custom">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Campagnes Email</h1>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 text-gray-600">
              <Users className="w-5 h-5" />
              <span>{subscribers.filter(s => s.is_active).length} Abonnés Actifs</span>
            </div>
            <Button
              variant="primary"
              icon={<Plus className="w-4 h-4" />}
              onClick={() => {
                resetForm();
                setActiveTab('create');
              }}
            >
              Nouvelle Campagne
            </Button>
          </div>
        </div>

        {error && (
          <Card className="mb-6 border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-3 text-red-700">
                <X className="w-5 h-5" />
                <span>{error}</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Tabs */}
        <div className="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg">
          <button
            onClick={() => setActiveTab('campaigns')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'campaigns'
                ? 'bg-white text-primary shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Mail className="w-4 h-4 inline mr-2" />
            Campagnes ({campaigns.length})
          </button>
          <button
            onClick={() => setActiveTab('create')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'create'
                ? 'bg-white text-primary shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Plus className="w-4 h-4 inline mr-2" />
            {editingCampaign ? 'Modifier' : 'Créer'}
          </button>
          <button
            onClick={() => setActiveTab('subscribers')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'subscribers'
                ? 'bg-white text-primary shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Users className="w-4 h-4 inline mr-2" />
            Abonnés ({subscribers.length})
          </button>
        </div>

        {/* Campaigns List */}
        {activeTab === 'campaigns' && (
          <div className="space-y-4">
            {campaigns.length === 0 ? (
              <Card>
                <CardContent className="p-12 text-center">
                  <Mail className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Aucune campagne
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Commencez par créer votre première campagne email.
                  </p>
                  <Button
                    variant="primary"
                    onClick={() => setActiveTab('create')}
                  >
                    Créer une campagne
                  </Button>
                </CardContent>
              </Card>
            ) : (
              campaigns.map((campaign) => (
                <Card key={campaign.id}>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">
                            {campaign.name}
                          </h3>
                          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(campaign.status)}`}>
                            {getStatusText(campaign.status)}
                          </span>
                        </div>
                        <p className="text-gray-600 mb-2">{campaign.subject}</p>
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <div className="flex items-center gap-1">
                            <Calendar className="w-4 h-4" />
                            <span>Créé: {new Date(campaign.created_at).toLocaleDateString('fr-FR')}</span>
                          </div>
                          {campaign.sent_at && (
                            <div className="flex items-center gap-1">
                              <Send className="w-4 h-4" />
                              <span>Envoyé: {new Date(campaign.sent_at).toLocaleDateString('fr-FR')}</span>
                            </div>
                          )}
                          {campaign.recipient_count > 0 && (
                            <div className="flex items-center gap-1">
                              <Users className="w-4 h-4" />
                              <span>{campaign.recipient_count} destinataires</span>
                            </div>
                          )}
                        </div>
                        {campaign.status === 'sent' && (
                          <div className="flex items-center gap-4 mt-2 text-sm">
                            <div className="flex items-center gap-1 text-blue-600">
                              <Eye className="w-4 h-4" />
                              <span>{campaign.open_count || 0} ouvertures</span>
                            </div>
                            <div className="flex items-center gap-1 text-green-600">
                              <BarChart3 className="w-4 h-4" />
                              <span>{campaign.click_count || 0} clics</span>
                            </div>
                          </div>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(campaign)}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(campaign.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        )}

        {/* Create/Edit Campaign Form */}
        {activeTab === 'create' && (
          <Card>
            <CardContent className="p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                {editingCampaign ? 'Modifier la Campagne' : 'Créer une Nouvelle Campagne'}
              </h2>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Nom de la campagne *
                    </label>
                    <Input
                      type="text"
                      value={campaignForm.name}
                      onChange={(e) => setCampaignForm({...campaignForm, name: e.target.value})}
                      placeholder="Ex: Offre Spéciale Été 2024"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Statut
                    </label>
                    <select
                      value={campaignForm.status}
                      onChange={(e) => setCampaignForm({...campaignForm, status: e.target.value})}
                      className="w-full p-2 border border-gray-300 rounded-lg"
                    >
                      <option value="draft">Brouillon</option>
                      <option value="scheduled">Programmé</option>
                      <option value="sent">Envoyé</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sujet de l&apos;email *
                  </label>
                  <Input
                    type="text"
                    value={campaignForm.subject}
                    onChange={(e) => setCampaignForm({...campaignForm, subject: e.target.value})}
                    placeholder="Sujet de votre email"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Contenu de l&apos;email *
                  </label>
                  <textarea
                    value={campaignForm.content}
                    onChange={(e) => setCampaignForm({...campaignForm, content: e.target.value})}
                    className="w-full h-48 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
                    placeholder="Rédigez le contenu de votre email ici..."
                    required
                  />
                </div>

                {campaignForm.status === 'scheduled' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Date et heure d&apos;envoi
                    </label>
                    <Input
                      type="datetime-local"
                      value={campaignForm.scheduled_at || ''}
                      onChange={(e) => setCampaignForm({...campaignForm, scheduled_at: e.target.value})}
                    />
                  </div>
                )}

                <div className="flex justify-end gap-4 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setActiveTab('campaigns');
                      resetForm();
                    }}
                  >
                    Annuler
                  </Button>
                  <Button
                    type="submit"
                    variant="primary"
                  >
                    {editingCampaign ? 'Modifier' : 'Créer'} la Campagne
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}

        {/* Subscribers List */}
        {activeTab === 'subscribers' && (
          <Card>
            <CardContent className="p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Abonnés Newsletter
              </h2>
              {subscribers.length === 0 ? (
                <div className="text-center py-12">
                  <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Aucun abonné
                  </h3>
                  <p className="text-gray-600">
                    Les abonnés à la newsletter apparaîtront ici.
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {subscribers.map((subscriber) => (
                    <div
                      key={subscriber.id}
                      className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <Mail className="w-5 h-5 text-gray-400" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {subscriber.first_name && subscriber.last_name
                              ? `${subscriber.first_name} ${subscriber.last_name}`
                              : subscriber.email}
                          </p>
                          {subscriber.first_name && subscriber.last_name && (
                            <p className="text-xs text-gray-500">{subscriber.email}</p>
                          )}
                          <p className="text-xs text-gray-500">
                            Abonné le {new Date(subscriber.subscribed_at).toLocaleDateString('fr-FR')}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <span
                          className={`px-2 py-1 text-xs rounded-full ${
                            subscriber.is_active
                              ? "bg-green-100 text-green-800"
                              : "bg-red-100 text-red-800"
                          }`}
                        >
                          {subscriber.is_active ? 'Actif' : 'Désabonné'}
                        </span>
                        {!subscriber.is_active && subscriber.unsubscribed_at && (
                          <span className="text-xs text-gray-500">
                            Désabonné le {new Date(subscriber.unsubscribed_at).toLocaleDateString('fr-FR')}
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
