// Note: This file contains both server and client functions
// Server functions should only be called from server components or API routes

/**
 * Booking Status Workflow Management
 * Handles booking status transitions with proper validation
 */

// Valid booking statuses and their allowed transitions
export const BOOKING_STATUSES = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed', 
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
};

export const PAYMENT_STATUSES = {
  PENDING: 'pending',
  PAID: 'paid',
  REFUNDED: 'refunded',
  FAILED: 'failed'
};

// Define allowed status transitions
const STATUS_TRANSITIONS = {
  [BOOKING_STATUSES.PENDING]: [BOOKING_STATUSES.CONFIRMED, BOOKING_STATUSES.CANCELLED],
  [BOOKING_STATUSES.CONFIRMED]: [BOOKING_STATUSES.COMPLETED, BOOKING_STATUSES.CANCELLED],
  [BOOKING_STATUSES.COMPLETED]: [BOOKING_STATUSES.CANCELLED], // Allow cancellation for refunds
  [BOOKING_STATUSES.CANCELLED]: [] // Terminal state
};

/**
 * Validate if a status transition is allowed
 */
export function isValidStatusTransition(currentStatus, newStatus) {
  if (!currentStatus || !newStatus) return false;
  if (currentStatus === newStatus) return true; // Same status is always valid
  
  const allowedTransitions = STATUS_TRANSITIONS[currentStatus] || [];
  return allowedTransitions.includes(newStatus);
}

/**
 * Get booking by ID with full details
 */
export async function getBookingById(bookingId) {
  try {
    const supabase = createServerSupabaseClient();
    
    const { data, error } = await supabase
      .from('bookings')
      .select(`
        *,
        customers (
          id,
          first_name,
          last_name,
          email,
          phone
        ),
        hotels (
          id,
          name,
          stars,
          cities (
            name,
            countries (
              name
            )
          )
        )
      `)
      .eq('id', bookingId)
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error fetching booking:', error);
    return { data: null, error };
  }
}

/**
 * Update booking status with validation and logging
 */
export async function updateBookingStatus(bookingId, newStatus, adminUserId, notes = null) {
  try {
    const supabase = createServerSupabaseClient();
    
    // First, get the current booking
    const { data: booking, error: fetchError } = await getBookingById(bookingId);
    if (fetchError || !booking) {
      throw new Error('Booking not found');
    }

    // Validate status transition
    if (!isValidStatusTransition(booking.status, newStatus)) {
      throw new Error(`Invalid status transition from ${booking.status} to ${newStatus}`);
    }

    // Update booking status
    const { data: updatedBooking, error: updateError } = await supabase
      .from('bookings')
      .update({
        status: newStatus,
        updated_at: new Date().toISOString(),
        ...(notes && { admin_notes: notes })
      })
      .eq('id', bookingId)
      .select()
      .single();

    if (updateError) throw updateError;

    // Log the status change (you could create a booking_status_history table for this)
    console.log(`Booking ${bookingId} status changed from ${booking.status} to ${newStatus} by admin ${adminUserId}`);

    return { data: updatedBooking, error: null };
  } catch (error) {
    console.error('Error updating booking status:', error);
    return { data: null, error };
  }
}

/**
 * Update payment status
 */
export async function updatePaymentStatus(bookingId, newPaymentStatus, adminUserId, notes = null) {
  try {
    const supabase = createServerSupabaseClient();
    
    // Validate payment status
    if (!Object.values(PAYMENT_STATUSES).includes(newPaymentStatus)) {
      throw new Error(`Invalid payment status: ${newPaymentStatus}`);
    }

    // Update payment status
    const { data: updatedBooking, error: updateError } = await supabase
      .from('bookings')
      .update({
        payment_status: newPaymentStatus,
        updated_at: new Date().toISOString(),
        ...(notes && { admin_notes: notes })
      })
      .eq('id', bookingId)
      .select()
      .single();

    if (updateError) throw updateError;

    console.log(`Booking ${bookingId} payment status changed to ${newPaymentStatus} by admin ${adminUserId}`);

    return { data: updatedBooking, error: null };
  } catch (error) {
    console.error('Error updating payment status:', error);
    return { data: null, error };
  }
}

/**
 * Get booking status statistics
 */
export async function getBookingStatusStats() {
  try {
    const supabase = createServerSupabaseClient();
    
    const { data, error } = await supabase
      .from('bookings')
      .select('status, payment_status');

    if (error) throw error;

    const stats = {
      total: data.length,
      byStatus: {},
      byPaymentStatus: {}
    };

    // Count by booking status
    Object.values(BOOKING_STATUSES).forEach(status => {
      stats.byStatus[status] = data.filter(b => b.status === status).length;
    });

    // Count by payment status
    Object.values(PAYMENT_STATUSES).forEach(status => {
      stats.byPaymentStatus[status] = data.filter(b => b.payment_status === status).length;
    });

    return { data: stats, error: null };
  } catch (error) {
    console.error('Error getting booking stats:', error);
    return { data: null, error };
  }
}

/**
 * Get bookings by status
 */
export async function getBookingsByStatus(status, limit = 50, offset = 0) {
  try {
    const supabase = createServerSupabaseClient();
    
    let query = supabase
      .from('bookings')
      .select(`
        *,
        customers (
          first_name,
          last_name,
          email,
          phone
        ),
        hotels (
          name,
          cities (
            name,
            countries (
              name
            )
          )
        )
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (status && status !== 'all') {
      query = query.eq('status', status);
    }

    const { data, error } = await query;

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error fetching bookings by status:', error);
    return { data: null, error };
  }
}
