"use client";
import { useState } from 'react';
import Image from 'next/image';
import { getOptimizedImageUrl, getResponsiveImageUrls } from '@/lib/cloudinary-client';

export const CloudinaryImage = ({
  publicId,
  alt,
  width,
  height,
  className = '',
  priority = false,
  quality = 'auto:good',
  crop = 'fill',
  gravity = 'auto',
  blur,
  grayscale,
  responsive = false,
  sizes,
  fill = false,
  placeholder = 'blur',
  ...props
}) => {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Fallback image
  const fallbackImage = '/images/placeholder.jpg';

  // Handle image error
  const handleError = () => {
    setImageError(true);
    setIsLoading(false);
  };

  // Handle image load
  const handleLoad = () => {
    setIsLoading(false);
  };

  // If no publicId or error, show fallback
  if (!publicId || imageError) {
    return (
      <Image
        src={fallbackImage}
        alt={alt || 'Image placeholder'}
        width={width}
        height={height}
        className={className}
        fill={fill}
        sizes={sizes}
        {...props}
      />
    );
  }

  // Generate optimized URL
  const imageUrl = getOptimizedImageUrl(publicId, {
    width: fill ? undefined : width,
    height: fill ? undefined : height,
    quality,
    crop,
    gravity,
    blur,
    grayscale
  });

  // Generate responsive URLs if needed
  const responsiveUrls = responsive ? getResponsiveImageUrls(publicId) : null;

  // Generate blur placeholder
  const blurDataURL = placeholder === 'blur' 
    ? getOptimizedImageUrl(publicId, { width: 10, height: 10, blur: 50, quality: 1 })
    : undefined;

  return (
    <div className={`relative ${isLoading ? 'animate-pulse bg-gray-200' : ''}`}>
      <Image
        src={imageUrl}
        alt={alt || 'Image'}
        width={fill ? undefined : width}
        height={fill ? undefined : height}
        fill={fill}
        className={className}
        priority={priority}
        sizes={sizes}
        placeholder={placeholder}
        blurDataURL={blurDataURL}
        onError={handleError}
        onLoad={handleLoad}
        {...props}
      />
      
      {/* Loading overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse rounded" />
      )}
    </div>
  );
};

// Specialized components for common use cases
export const HotelImage = ({ publicId, alt, className = '', priority = false }) => (
  <CloudinaryImage
    publicId={publicId}
    alt={alt}
    fill
    className={`object-cover ${className}`}
    priority={priority}
    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
  />
);

export const HotelThumbnail = ({ publicId, alt, className = '' }) => (
  <CloudinaryImage
    publicId={publicId}
    alt={alt}
    width={150}
    height={150}
    className={`object-cover rounded ${className}`}
    crop="thumb"
    gravity="face"
  />
);

export const HotelGalleryImage = ({ publicId, alt, className = '', onClick }) => (
  <div 
    className={`relative cursor-pointer ${className}`}
    onClick={onClick}
  >
    <CloudinaryImage
      publicId={publicId}
      alt={alt}
      fill
      className="object-cover transition-transform hover:scale-105"
      sizes="(max-width: 768px) 50vw, 25vw"
    />
  </div>
);

export const ResponsiveHotelImage = ({ publicId, alt, className = '', priority = false }) => {
  const responsiveUrls = getResponsiveImageUrls(publicId);
  
  return (
    <picture>
      <source 
        media="(max-width: 640px)" 
        srcSet={responsiveUrls.small} 
      />
      <source 
        media="(max-width: 1024px)" 
        srcSet={responsiveUrls.medium} 
      />
      <source 
        media="(min-width: 1025px)" 
        srcSet={responsiveUrls.large} 
      />
      <CloudinaryImage
        publicId={publicId}
        alt={alt}
        fill
        className={`object-cover ${className}`}
        priority={priority}
        sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
      />
    </picture>
  );
};

export default CloudinaryImage;
