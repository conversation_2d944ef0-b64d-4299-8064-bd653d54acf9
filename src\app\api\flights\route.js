// ✅ /app/api/flights/route.js

export async function GET() {
  const algerianAirports = ["ALG", "ORN", "CZL", "TLM", "AAE", "BJA", "GHA", "ELG"];
  const tunisianAirports = ["TUN", "NBE", "MIR", "SFA", "TOE"];
  const today = new Date().toISOString().split("T")[0];

  try {
    // Get Amadeus Access Token
    const tokenRes = await fetch("https://test.api.amadeus.com/v1/security/oauth2/token", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        grant_type: "client_credentials",
        client_id: process.env.AMADEUS_CLIENT_ID,
        client_secret: process.env.AMADEUS_CLIENT_SECRET,
      }),
    });

    const { access_token } = await tokenRes.json();
    const allFlights = [];

    for (const from of algerianAirports) {
      for (const to of tunisianAirports) {
        const res = await fetch(
          `https://test.api.amadeus.com/v2/shopping/flight-offers?originLocationCode=${from}&destinationLocationCode=${to}&departureDate=${today}&adults=1&max=2`,
          {
            headers: {
              Authorization: `Bearer ${access_token}`,
            },
          }
        );

        const data = await res.json();
        if (data.data) {
          allFlights.push(...data.data);
        }
      }
    }

    return Response.json({ data: allFlights });
  } catch (error) {
    console.error("Error fetching flights:", error);
    return new Response("Failed to fetch flights", { status: 500 });
  }
}
