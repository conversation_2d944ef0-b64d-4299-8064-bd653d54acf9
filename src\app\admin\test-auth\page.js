"use client";

import { useSession } from "@/components/providers/SessionProvider";
import { Card, CardContent } from "@/components/ui/Card";

export default function TestAuthPage() {
  const { user, profile, session, loading, isAdmin } = useSession();

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Authentication Test
        </h1>

        <div className="grid gap-6">
          {/* Session Info */}
          <Card>
            <CardContent className="p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Session Information
              </h2>
              <div className="space-y-2">
                <p><strong>Authenticated:</strong> {session ? 'Yes' : 'No'}</p>
                <p><strong>User ID:</strong> {user?.id || 'N/A'}</p>
                <p><strong>Email:</strong> {user?.email || 'N/A'}</p>
                <p><strong>Is Admin:</strong> {isAdmin() ? 'Yes' : 'No'}</p>
                <p><strong>Profile Role:</strong> {profile?.role || 'N/A'}</p>
              </div>
            </CardContent>
          </Card>

          {/* Session Details */}
          {session && (
            <Card>
              <CardContent className="p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  Session Details
                </h2>
                <div className="space-y-2 text-sm">
                  <p><strong>Access Token:</strong> {session.access_token ? 'Present' : 'Missing'}</p>
                  <p><strong>Refresh Token:</strong> {session.refresh_token ? 'Present' : 'Missing'}</p>
                  <p><strong>Expires At:</strong> {session.expires_at ? new Date(session.expires_at * 1000).toLocaleString() : 'N/A'}</p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Profile Details */}
          {profile && (
            <Card>
              <CardContent className="p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  Profile Details
                </h2>
                <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                  {JSON.stringify(profile, null, 2)}
                </pre>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
