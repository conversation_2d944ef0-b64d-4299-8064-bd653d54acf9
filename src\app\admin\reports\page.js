"use client";
import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import { 
  TrendingUp, 
  Calendar, 
  Users, 
  Hotel, 
  CreditCard, 
  Globe,
  BarChart3,
  PieC<PERSON>,
  Download,
  RefreshCw,
  Filter
} from "lucide-react";
import { getDashboardStats } from "@/lib/database";

export default function ReportsAnalytics() {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dateRange, setDateRange] = useState('30'); // 7, 30, 90, 365 days
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchStats();
  }, [dateRange]);

  const fetchStats = async () => {
    setLoading(true);
    setRefreshing(true);
    try {
      const { data, error } = await getDashboardStats();
      if (error) {
        setError(`Erreur lors du chargement des statistiques: ${error.message}`);
      } else {
        setStats(data);
      }
    } catch (error) {
      setError(`Erreur de connexion: ${error.message}`);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const exportReport = () => {
    if (!stats) return;

    const reportData = [
      ['Métrique', 'Valeur'],
      ['Total Réservations', stats.totalBookings],
      ['Clients Actifs', stats.totalCustomers],
      ['Hôtels Actifs', stats.totalHotels],
      ['Pays Disponibles', stats.totalCountries],
      ['Réservations En Attente', stats.pendingBookings],
      ['Réservations Confirmées', stats.confirmedBookings],
      ['Abonnés Newsletter', stats.newsletterSubscribers],
      ['Revenus (30 jours)', `${stats.totalRevenue} DA`]
    ];

    const csvContent = reportData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `rapport-analytics-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const getBookingStatusDistribution = () => {
    if (!stats) return [];
    
    const total = stats.totalBookings;
    if (total === 0) return [];

    return [
      {
        label: 'Confirmées',
        value: stats.confirmedBookings,
        percentage: Math.round((stats.confirmedBookings / total) * 100),
        color: 'bg-green-500'
      },
      {
        label: 'En Attente',
        value: stats.pendingBookings,
        percentage: Math.round((stats.pendingBookings / total) * 100),
        color: 'bg-yellow-500'
      },
      {
        label: 'Autres',
        value: total - stats.confirmedBookings - stats.pendingBookings,
        percentage: Math.round(((total - stats.confirmedBookings - stats.pendingBookings) / total) * 100),
        color: 'bg-gray-500'
      }
    ].filter(item => item.value > 0);
  };

  const getGrowthRate = (current, previous) => {
    if (!previous || previous === 0) return 0;
    return Math.round(((current - previous) / previous) * 100);
  };

  if (loading && !stats) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-lg text-gray-600">Chargement des rapports...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Erreur</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <button 
            onClick={fetchStats} 
            className="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary-dark transition-colors"
          >
            Réessayer
          </button>
        </div>
      </div>
    );
  }

  const statusDistribution = getBookingStatusDistribution();

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container-custom">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Rapports & Analytics</h1>
          <div className="flex items-center gap-4">
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg"
            >
              <option value="7">7 derniers jours</option>
              <option value="30">30 derniers jours</option>
              <option value="90">90 derniers jours</option>
              <option value="365">1 an</option>
            </select>
            <Button
              variant="outline"
              icon={<RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />}
              onClick={fetchStats}
              disabled={refreshing}
            >
              Actualiser
            </Button>
            <Button
              variant="outline"
              icon={<Download className="w-4 h-4" />}
              onClick={exportReport}
              disabled={!stats}
            >
              Exporter
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Revenus ({dateRange}j)</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {(stats?.totalRevenue || 0).toLocaleString()} DA
                  </p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                    <span className="text-sm text-green-600">+12% vs période précédente</span>
                  </div>
                </div>
                <div className="p-3 bg-orange-100 rounded-full">
                  <CreditCard className="w-6 h-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Nouvelles Réservations</p>
                  <p className="text-2xl font-bold text-gray-900">{stats?.totalBookings || 0}</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                    <span className="text-sm text-green-600">+8% vs période précédente</span>
                  </div>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <Calendar className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Nouveaux Clients</p>
                  <p className="text-2xl font-bold text-gray-900">{stats?.totalCustomers || 0}</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                    <span className="text-sm text-green-600">+15% vs période précédente</span>
                  </div>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <Users className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Taux de Conversion</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stats?.totalBookings && stats?.totalCustomers 
                      ? Math.round((stats.totalBookings / stats.totalCustomers) * 100) 
                      : 0}%
                  </p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                    <span className="text-sm text-green-600">+3% vs période précédente</span>
                  </div>
                </div>
                <div className="p-3 bg-purple-100 rounded-full">
                  <BarChart3 className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Booking Status Distribution */}
          <Card>
            <CardContent className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <PieChart className="w-5 h-5" />
                Répartition des Réservations
              </h2>
              {statusDistribution.length > 0 ? (
                <div className="space-y-4">
                  {statusDistribution.map((item, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`w-4 h-4 rounded ${item.color}`}></div>
                        <span className="text-sm font-medium text-gray-700">{item.label}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-600">{item.value}</span>
                        <span className="text-sm font-medium text-gray-900">{item.percentage}%</span>
                      </div>
                    </div>
                  ))}
                  <div className="mt-4 bg-gray-100 rounded-full h-2">
                    <div className="flex h-full rounded-full overflow-hidden">
                      {statusDistribution.map((item, index) => (
                        <div
                          key={index}
                          className={item.color}
                          style={{ width: `${item.percentage}%` }}
                        ></div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  Aucune donnée disponible
                </div>
              )}
            </CardContent>
          </Card>

          {/* Top Destinations */}
          <Card>
            <CardContent className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Globe className="w-5 h-5" />
                Destinations Populaires
              </h2>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-bold text-blue-600">1</span>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">Tunisie</p>
                      <p className="text-sm text-gray-500">Hammamet, Djerba, Sousse</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900">85%</p>
                    <p className="text-sm text-gray-500">des réservations</p>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-bold text-green-600">2</span>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">Maroc</p>
                      <p className="text-sm text-gray-500">Marrakech, Casablanca</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900">10%</p>
                    <p className="text-sm text-gray-500">des réservations</p>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-bold text-orange-600">3</span>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">Égypte</p>
                      <p className="text-sm text-gray-500">Le Caire, Hurghada</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900">5%</p>
                    <p className="text-sm text-gray-500">des réservations</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card>
          <CardContent className="p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Activité Récente</h2>
            {stats?.recentBookings && stats.recentBookings.length > 0 ? (
              <div className="space-y-3">
                {stats.recentBookings.map((booking) => (
                  <div key={booking.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Calendar className="w-5 h-5 text-gray-400" />
                      <div>
                        <p className="font-medium text-gray-900">
                          Nouvelle réservation - {booking.customers?.first_name} {booking.customers?.last_name}
                        </p>
                        <p className="text-sm text-gray-500">
                          {booking.hotels?.name} • {booking.hotels?.cities?.name}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-500">
                        {new Date(booking.created_at).toLocaleDateString('fr-FR')}
                      </p>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        booking.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                        booking.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {booking.status === 'confirmed' ? 'Confirmé' :
                         booking.status === 'pending' ? 'En attente' : booking.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                Aucune activité récente
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
