"use client";
import { useState } from "react";
import {
  FaSearch,
  FaMapMarkerAlt,
  FaCalendarAlt,
  FaUsers,
} from "react-icons/fa";
import { Card } from "@/components/ui/Card";
import Button from "@/components/ui/Button";

export default function BookingSearchBox({ className = "", style = {} }) {
  const [searchData, setSearchData] = useState({
    location: "",
    arrival: "",
    departure: "",
    guests: "",
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSearch = (e) => {
    e.preventDefault();
    console.log("Search data:", searchData);
  };

  return (
    <Card
      className={`relative rounded-3xl travel-card-shadow max-w-6xl w-[95%] mx-auto p-6 md:p-10 bg-white ${className}`}
      style={style}
    >
      <h1 className="text-3xl md:text-5xl font-bold text-blue-500 mb-3">
        Bonjour !
      </h1>
      <p className="text-lg md:text-2xl text-gray-600 mb-6">
        Explorez de magnifiques endroits dans le monde avec nous
      </p>

      <form
        onSubmit={handleSearch}
        className="grid grid-cols-1 md:grid-cols-5 gap-6 items-end"
      >
        {/* Location */}
        <div className="flex flex-col">
          <label className="flex items-center gap-2 text-gray-700 text-lg font-semibold mb-2">
            <FaMapMarkerAlt className="text-blue-500" />
            Lieu
          </label>
          <input
            type="text"
            name="location"
            placeholder="Ajouter une destination"
            value={searchData.location}
            onChange={handleInputChange}
            className="w-full border border-gray-300 rounded-xl px-4 py-3 text-base focus:outline-none focus:ring-2 focus:ring-accent"
          />
        </div>

        {/* Arrival */}
        <div className="flex flex-col">
          <label className="flex items-center gap-2 text-gray-700 text-lg font-semibold mb-2">
            <FaCalendarAlt className="text-blue-500" />
            Arrivée
          </label>
          <input
            type="date"
            name="arrival"
            value={searchData.arrival}
            onChange={handleInputChange}
            className="w-full border border-gray-300 rounded-xl px-4 py-3 text-base focus:outline-none focus:ring-2 focus:ring-accent"
          />
        </div>

        {/* Departure */}
        <div className="flex flex-col">
          <label className="flex items-center gap-2 text-gray-700 text-lg font-semibold mb-2">
            <FaCalendarAlt className="text-blue-500" />
            Départ
          </label>
          <input
            type="date"
            name="departure"
            value={searchData.departure}
            onChange={handleInputChange}
            className="w-full border border-gray-300 rounded-xl px-4 py-3 text-base focus:outline-none focus:ring-2 focus:ring-accent"
          />
        </div>

        {/* Guests */}
        <div className="flex flex-col">
          <label className="flex items-center gap-2 text-gray-700 text-lg font-semibold mb-2">
            <FaUsers className="text-blue-500" />
            Invités
          </label>
          <input
            type="number"
            name="guests"
            min="1"
            placeholder="1+"
            value={searchData.guests}
            onChange={handleInputChange}
            className="w-full border border-gray-300 rounded-xl px-4 py-3 text-base focus:outline-none focus:ring-2 focus:ring-accent"
          />
        </div>

        {/* Search Button */}
        <div className="flex justify-end md:justify-center">
          <Button
            type="submit"
            variant="secondary"
            className="w-full h-[58px] rounded-xl text-lg font-semibold flex items-center justify-center gap-2 bg-blue-500 hover:bg-accent text-white transition-all"
          >
            <FaSearch className="text-xl" />
            Rechercher
          </Button>
        </div>
      </form>
    </Card>
  );
}
