/**
 * Utility function to merge class names
 * @param {...any} inputs - Class names to merge
 * @returns {string} Merged class names
 */
export function cn(...inputs) {
  return inputs.flat().filter(Boolean).join(" ").replace(/\s+/g, " ").trim();
}

/**
 * Utility function to format currency
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code (default: 'DZD')
 * @returns {string} Formatted currency string
 */
export function formatCurrency(amount, currency = "DZD") {
  return new Intl.NumberFormat("fr-DZ", {
    style: "currency",
    currency: currency,
  }).format(amount);
}

/**
 * Utility function to format date
 * @param {Date|string} date - Date to format
 * @param {string} locale - Locale (default: 'fr-DZ')
 * @returns {string} Formatted date string
 */
export function formatDate(date, locale = "fr-DZ") {
  return new Intl.DateTimeFormat(locale, {
    year: "numeric",
    month: "long",
    day: "numeric",
  }).format(new Date(date));
}

/**
 * Utility function to truncate text
 * @param {string} text - Text to truncate
 * @param {number} length - Maximum length
 * @returns {string} Truncated text
 */
export function truncateText(text, length = 100) {
  if (text.length <= length) return text;
  return text.slice(0, length) + "...";
}

/**
 * Utility function to generate slug from text
 * @param {string} text - Text to convert to slug
 * @returns {string} Slug
 */
export function generateSlug(text) {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, "")
    .replace(/[\s_-]+/g, "-")
    .replace(/^-+|-+$/g, "");
}

/**
 * Utility function to debounce function calls
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
export function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * Validates if a string is a valid URL
 * @param {string} url - The URL to validate
 * @returns {boolean} - True if valid URL, false otherwise
 */
export function isValidUrl(url) {
  if (!url || typeof url !== 'string') return false;

  try {
    const urlObj = new URL(url);
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
  } catch {
    return false;
  }
}

/**
 * Validates if a URL is a valid location/maps URL
 * @param {string} url - The URL to validate
 * @returns {object} - Validation result with isValid and message
 */
export function validateLocationUrl(url) {
  if (!url) {
    return { isValid: true, message: '' }; // Empty URL is valid (optional field)
  }

  if (!isValidUrl(url)) {
    return {
      isValid: false,
      message: 'Veuillez entrer une URL valide (doit commencer par http:// ou https://)'
    };
  }

  // Check if it's a recognized mapping service
  const mapServices = [
    'maps.google.com',
    'maps.app.goo.gl',
    'goo.gl/maps',
    'maps.apple.com',
    'bing.com/maps',
    'openstreetmap.org',
    'waze.com'
  ];

  const urlObj = new URL(url);
  const isMapService = mapServices.some(service =>
    urlObj.hostname.includes(service) || urlObj.href.includes(service)
  );

  if (!isMapService) {
    return {
      isValid: true, // Still valid, just a warning
      message: 'Conseil: Utilisez de préférence Google Maps, Apple Maps, ou un autre service de cartographie reconnu',
      isWarning: true
    };
  }

  return { isValid: true, message: '' };
}
