import { NextResponse } from 'next/server';
import { testCloudinaryConnection } from '@/lib/cloudinary';

export async function POST() {
  try {
    const result = await testCloudinaryConnection();
    return NextResponse.json(result);
  } catch (error) {
    console.error('Cloudinary test API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Erreur lors du test de connexion Cloudinary',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
