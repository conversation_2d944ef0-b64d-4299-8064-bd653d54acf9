import { NextResponse } from "next/server";
import { createServerSupabaseClient } from "@/lib/auth";

export async function GET(request, { params }) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: "Hotel ID is required" },
        { status: 400 }
      );
    }

    const supabase = await createServerSupabaseClient();

    // Fetch hotel with all related data
    const { data: hotel, error } = await supabase
      .from("hotels")
      .select(
        `
        *,
        cities (
          id,
          name,
          region,
          description,
          image_url,
          highlights,
          best_time,
          duration,
          countries (
            id,
            name,
            code,
            currency
          )
        )
      `
      )
      .eq("id", id)
      .eq("is_active", true)
      .single();

    if (error) {
      console.error("Error fetching hotel:", error);
      if (error.code === "PGRST116") {
        return NextResponse.json({ error: "Hotel not found" }, { status: 404 });
      }
      return NextResponse.json(
        { error: "Failed to fetch hotel" },
        { status: 500 }
      );
    }

    if (!hotel) {
      return NextResponse.json({ error: "Hotel not found" }, { status: 404 });
    }

    // Process hotel images using new URL fields
    const galleryImages = [];

    // Collect all additional image URLs
    if (hotel.image_url_1) {
      galleryImages.push({
        id: "url-1",
        url: hotel.image_url_1,
        alt: `${hotel.name} - Image 1`,
        width: 800,
        height: 600,
      });
    }

    if (hotel.image_url_2) {
      galleryImages.push({
        id: "url-2",
        url: hotel.image_url_2,
        alt: `${hotel.name} - Image 2`,
        width: 800,
        height: 600,
      });
    }

    if (hotel.image_url_3) {
      galleryImages.push({
        id: "url-3",
        url: hotel.image_url_3,
        alt: `${hotel.name} - Image 3`,
        width: 800,
        height: 600,
      });
    }

    if (hotel.image_url_4) {
      galleryImages.push({
        id: "url-4",
        url: hotel.image_url_4,
        alt: `${hotel.name} - Image 4`,
        width: 800,
        height: 600,
      });
    }

    const processedHotel = {
      ...hotel,
      images: {
        primary: hotel.image_url || "/images/back.png",
        gallery: galleryImages,
      },
    };

    // Add fallback images if no gallery images exist
    if (processedHotel.images.gallery.length === 0) {
      const fallbackImages = [
        "/images/exploretunisia/exploretunisia2.jpg",
        "/images/exploretunisia/exploretunisia1.jpg",
        "/images/exploretunisia/exploretunisia3.jpg",
        "/images/back.png",
      ];

      processedHotel.images.gallery = fallbackImages.map((url, index) => ({
        id: `fallback-${index}`,
        url,
        alt: `${hotel.name} - Image ${index + 1}`,
        width: 800,
        height: 600,
      }));
    }

    // Ensure primary image exists
    if (!processedHotel.images.primary) {
      processedHotel.images.primary =
        processedHotel.images.gallery[0]?.url || "/images/back.png";
    }

    return NextResponse.json({
      success: true,
      data: processedHotel,
    });
  } catch (error) {
    console.error("API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
