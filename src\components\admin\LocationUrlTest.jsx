"use client";
import { useState } from 'react';
import { validateLocationUrl } from '@/lib/utils';
import Input from '@/components/ui/Input';
import Button from '@/components/ui/Button';
import { Card, CardContent } from '@/components/ui/Card';

/**
 * Test component for location URL validation
 * This component can be used to test the location URL functionality
 */
export function LocationUrlTest() {
  const [testUrl, setTestUrl] = useState('');
  const [validation, setValidation] = useState({ isValid: true, message: '' });

  const handleUrlChange = (url) => {
    setTestUrl(url);
    const result = validateLocationUrl(url);
    setValidation(result);
  };

  const testUrls = [
    'https://maps.google.com/maps?q=36.8065,10.1815',
    'https://maps.app.goo.gl/example',
    'https://maps.apple.com/?q=Tunis,Tunisia',
    'https://www.bing.com/maps?q=Tunis',
    'https://www.openstreetmap.org/#map=15/36.8065/10.1815',
    'https://example.com/not-a-map',
    'invalid-url',
    ''
  ];

  return (
    <Card className="max-w-2xl mx-auto">
      <CardContent className="p-6">
        <h2 className="text-2xl font-semibold mb-4">Test de validation d'URL de localisation</h2>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              URL de localisation à tester
            </label>
            <Input
              type="url"
              value={testUrl}
              onChange={(e) => handleUrlChange(e.target.value)}
              placeholder="https://maps.google.com/..."
              className={`${!validation.isValid ? 'border-red-500' : validation.isWarning ? 'border-yellow-500' : ''}`}
            />
            {validation.message && (
              <p className={`text-xs mt-1 ${
                !validation.isValid ? 'text-red-600' : 
                validation.isWarning ? 'text-yellow-600' : 'text-gray-500'
              }`}>
                {validation.message}
              </p>
            )}
          </div>

          <div>
            <h3 className="text-lg font-medium mb-2">Résultat de la validation:</h3>
            <div className={`p-3 rounded-lg ${
              validation.isValid ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
            }`}>
              <strong>Valide:</strong> {validation.isValid ? 'Oui' : 'Non'}
              {validation.isWarning && <span className="ml-2 text-yellow-600">(Avertissement)</span>}
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-2">URLs de test:</h3>
            <div className="space-y-2">
              {testUrls.map((url, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => handleUrlChange(url)}
                  className="mr-2 mb-2"
                >
                  {url || '(URL vide)'}
                </Button>
              ))}
            </div>
          </div>

          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Services de cartographie supportés:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Google Maps (maps.google.com, maps.app.goo.gl)</li>
              <li>• Apple Maps (maps.apple.com)</li>
              <li>• Bing Maps (bing.com/maps)</li>
              <li>• OpenStreetMap (openstreetmap.org)</li>
              <li>• Waze (waze.com)</li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
