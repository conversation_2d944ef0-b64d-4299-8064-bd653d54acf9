"use client";
import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import { 
  Mail, 
  Users, 
  Plus, 
  Search, 
  Download, 
  UserMinus, 
  UserPlus,
  Calendar,
  Filter,
  X
} from "lucide-react";
import { 
  getNewsletterSubscribers, 
  createNewsletterSubscriber, 
  updateNewsletterSubscriber,
  unsubscribeNewsletter 
} from "@/lib/database";

export default function NewsletterManagement() {
  const [subscribers, setSubscribers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all"); // all, active, inactive
  const [isAddingSubscriber, setIsAddingSubscriber] = useState(false);

  const [subscriberForm, setSubscriberForm] = useState({
    email: '',
    first_name: '',
    last_name: '',
    is_active: true
  });

  useEffect(() => {
    fetchSubscribers();
  }, []);

  const fetchSubscribers = async () => {
    setLoading(true);
    try {
      const { data, error } = await getNewsletterSubscribers(false); // Get all subscribers
      if (error) {
        setError(`Erreur lors du chargement des abonnés: ${error.message}`);
      } else {
        setSubscribers(data || []);
      }
    } catch (error) {
      setError(`Erreur de connexion: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setSubscriberForm({
      email: '',
      first_name: '',
      last_name: '',
      is_active: true
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const { data, error } = await createNewsletterSubscriber(subscriberForm);
      if (error) throw error;
      setSubscribers([data, ...subscribers]);
      setIsAddingSubscriber(false);
      resetForm();
    } catch (error) {
      console.error('Error adding subscriber:', error);
      alert('Erreur lors de l\'ajout de l\'abonné');
    }
  };

  const handleUnsubscribe = async (email) => {
    if (!confirm('Êtes-vous sûr de vouloir désabonner cet utilisateur ?')) return;
    
    try {
      const { data, error } = await unsubscribeNewsletter(email);
      if (error) throw error;
      setSubscribers(subscribers.map(s => 
        s.email === email ? { ...s, is_active: false, unsubscribed_at: new Date().toISOString() } : s
      ));
    } catch (error) {
      console.error('Error unsubscribing:', error);
      alert('Erreur lors du désabonnement');
    }
  };

  const handleResubscribe = async (id) => {
    try {
      const { data, error } = await updateNewsletterSubscriber(id, { 
        is_active: true, 
        unsubscribed_at: null 
      });
      if (error) throw error;
      setSubscribers(subscribers.map(s => 
        s.id === id ? { ...s, is_active: true, unsubscribed_at: null } : s
      ));
    } catch (error) {
      console.error('Error resubscribing:', error);
      alert('Erreur lors du réabonnement');
    }
  };

  const exportSubscribers = () => {
    const activeSubscribers = subscribers.filter(s => s.is_active);
    const csvContent = [
      ['Email', 'Prénom', 'Nom', 'Date d\'abonnement'],
      ...activeSubscribers.map(s => [
        s.email,
        s.first_name || '',
        s.last_name || '',
        new Date(s.subscribed_at).toLocaleDateString('fr-FR')
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `newsletter-subscribers-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const filteredSubscribers = subscribers.filter(subscriber => {
    const matchesSearch = 
      subscriber.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      subscriber.first_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      subscriber.last_name?.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = 
      statusFilter === "all" || 
      (statusFilter === "active" && subscriber.is_active) ||
      (statusFilter === "inactive" && !subscriber.is_active);
    
    return matchesSearch && matchesStatus;
  });

  const activeCount = subscribers.filter(s => s.is_active).length;
  const inactiveCount = subscribers.filter(s => !s.is_active).length;

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-lg text-gray-600">Chargement des abonnés...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container-custom">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Newsletter</h1>
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              icon={<Download className="w-4 h-4" />}
              onClick={exportSubscribers}
              disabled={activeCount === 0}
            >
              Exporter ({activeCount})
            </Button>
            <Button
              variant="primary"
              icon={<Plus className="w-4 h-4" />}
              onClick={() => setIsAddingSubscriber(true)}
            >
              Ajouter un Abonné
            </Button>
          </div>
        </div>

        {error && (
          <Card className="mb-6 border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-3 text-red-700">
                <X className="w-5 h-5" />
                <span>{error}</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Abonnés</p>
                  <p className="text-2xl font-bold text-gray-900">{subscribers.length}</p>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <Users className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Abonnés Actifs</p>
                  <p className="text-2xl font-bold text-green-600">{activeCount}</p>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <UserPlus className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Désabonnés</p>
                  <p className="text-2xl font-bold text-red-600">{inactiveCount}</p>
                </div>
                <div className="p-3 bg-red-100 rounded-full">
                  <UserMinus className="w-6 h-6 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <Input
                  type="text"
                  placeholder="Rechercher par email ou nom..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  icon={<Search className="w-4 h-4" />}
                />
              </div>
              <div className="flex gap-2">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg"
                >
                  <option value="all">Tous les statuts</option>
                  <option value="active">Actifs seulement</option>
                  <option value="inactive">Désabonnés seulement</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Subscribers List */}
        <Card>
          <CardContent className="p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              Abonnés ({filteredSubscribers.length})
            </h2>
            
            {filteredSubscribers.length === 0 ? (
              <div className="text-center py-12">
                <Mail className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {searchQuery || statusFilter !== 'all' ? 'Aucun résultat' : 'Aucun abonné'}
                </h3>
                <p className="text-gray-600">
                  {searchQuery || statusFilter !== 'all' 
                    ? 'Essayez de modifier vos critères de recherche.'
                    : 'Les abonnés à la newsletter apparaîtront ici.'}
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {filteredSubscribers.map((subscriber) => (
                  <div
                    key={subscriber.id}
                    className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <Mail className="w-5 h-5 text-gray-400" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {subscriber.first_name && subscriber.last_name 
                            ? `${subscriber.first_name} ${subscriber.last_name}` 
                            : subscriber.email}
                        </p>
                        {subscriber.first_name && subscriber.last_name && (
                          <p className="text-xs text-gray-500">{subscriber.email}</p>
                        )}
                        <div className="flex items-center gap-4 text-xs text-gray-500 mt-1">
                          <div className="flex items-center gap-1">
                            <Calendar className="w-3 h-3" />
                            <span>Abonné: {new Date(subscriber.subscribed_at).toLocaleDateString('fr-FR')}</span>
                          </div>
                          {!subscriber.is_active && subscriber.unsubscribed_at && (
                            <div className="flex items-center gap-1">
                              <UserMinus className="w-3 h-3" />
                              <span>Désabonné: {new Date(subscriber.unsubscribed_at).toLocaleDateString('fr-FR')}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <span
                        className={`px-2 py-1 text-xs rounded-full ${
                          subscriber.is_active
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {subscriber.is_active ? 'Actif' : 'Désabonné'}
                      </span>
                      {subscriber.is_active ? (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleUnsubscribe(subscriber.email)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <UserMinus className="w-4 h-4" />
                        </Button>
                      ) : (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleResubscribe(subscriber.id)}
                          className="text-green-600 hover:text-green-700"
                        >
                          <UserPlus className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Add Subscriber Modal */}
        {isAddingSubscriber && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <Card className="w-full max-w-md mx-4">
              <CardContent className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold text-gray-900">
                    Ajouter un Abonné
                  </h2>
                  <button 
                    onClick={() => {
                      setIsAddingSubscriber(false);
                      resetForm();
                    }} 
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <X className="w-6 h-6" />
                  </button>
                </div>
                
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email *
                    </label>
                    <Input
                      type="email"
                      value={subscriberForm.email}
                      onChange={(e) => setSubscriberForm({...subscriberForm, email: e.target.value})}
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Prénom
                      </label>
                      <Input
                        type="text"
                        value={subscriberForm.first_name}
                        onChange={(e) => setSubscriberForm({...subscriberForm, first_name: e.target.value})}
                        placeholder="Prénom"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Nom
                      </label>
                      <Input
                        type="text"
                        value={subscriberForm.last_name}
                        onChange={(e) => setSubscriberForm({...subscriberForm, last_name: e.target.value})}
                        placeholder="Nom"
                      />
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="is-active"
                      checked={subscriberForm.is_active}
                      onChange={(e) => setSubscriberForm({...subscriberForm, is_active: e.target.checked})}
                      className="mr-2"
                    />
                    <label htmlFor="is-active" className="text-sm text-gray-700">
                      Abonné actif
                    </label>
                  </div>
                  
                  <div className="flex justify-end gap-4 pt-4">
                    <Button 
                      type="button"
                      variant="outline" 
                      onClick={() => {
                        setIsAddingSubscriber(false);
                        resetForm();
                      }}
                    >
                      Annuler
                    </Button>
                    <Button type="submit" variant="primary">
                      Ajouter
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}
