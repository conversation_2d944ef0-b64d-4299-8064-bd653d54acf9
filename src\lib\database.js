import supabase from '@/Config/supabase';
import { validateEnvironment } from './env-check';

// Validate environment on module load
const envCheck = validateEnvironment();
if (!envCheck.isValid) {
  console.error('❌ Supabase configuration error:');
  envCheck.report();
}

// ===== COUNTRIES =====
export const getCountries = async (featuredOnly = false) => {
  try {
    let query = supabase.from('countries').select('*');
    
    if (featuredOnly) {
      query = query.eq('is_featured', true);
    }
    
    const { data, error } = await query.order('name');
    
    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error fetching countries:', error);
    return { data: null, error };
  }
};

export async function getTopHotels() {
  const { data, error } = await supabase
    .from("hotels")
    .select("*")
    .eq("top_book_now", true)
    .eq("is_active", true);

  if (error) {
    console.error("Supabase error:", error);
    return [];
  }

  console.log("✅ getTopHotels returned:", data);
  return data;
}

export async function toggleTopBookNow(hotelId, currentValue) {
  const { data, error } = await supabase
    .from("hotels")
    .update({ top_book_now: !currentValue })
    .eq("id", hotelId);

  if (error) {
    console.error("Toggle error:", error.message);
    throw error;
  }

  return data;
}


export const createCountry = async (countryData) => {
  try {
    const { data, error } = await supabase
      .from('countries')
      .insert([countryData])
      .select()
      .single();
    
    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error creating country:', error);
    return { data: null, error };
  }
};

export const updateCountry = async (id, countryData) => {
  try {
    const { data, error } = await supabase
      .from('countries')
      .update(countryData)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error updating country:', error);
    return { data: null, error };
  }
};

// ===== CITIES =====
export const getCities = async (countryId = null, featuredOnly = false) => {
  try {
    let query = supabase
      .from('cities')
      .select(`
        *,
        countries (
          name,
          code,
          currency,
          is_featured
        )
      `);
    
    if (countryId) {
      query = query.eq('country_id', countryId);
    }
    
    if (featuredOnly) {
      query = query.eq('is_featured', true);
    }
    
    const { data, error } = await query.order('name');
    
    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error fetching cities:', error);
    return { data: null, error };
  }
};

export const getCityById = async (id) => {
  try {
    const { data, error } = await supabase
      .from('cities')
      .select(`
        *,
        countries (
          name,
          code,
          currency,
          is_featured
        )
      `)
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error fetching city:', error);
    return { data: null, error };
  }
};

export const createCity = async (cityData) => {
  try {
    const { data, error } = await supabase
      .from('cities')
      .insert([cityData])
      .select()
      .single();
    
    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error creating city:', error);
    return { data: null, error };
  }
};

export const updateCity = async (id, cityData) => {
  try {
    const { data, error } = await supabase
      .from('cities')
      .update(cityData)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error updating city:', error);
    return { data: null, error };
  }
};

// ===== HOTELS =====
export const getHotels = async (cityId = null, featuredOnly = false, activeOnly = true) => {
  try {
    let query = supabase
      .from('hotels')
      .select(`
        *,
        cities (
          name,
          region,
          countries (
            name,
            code,
            currency
          )
        )
      `);
    
    if (cityId) {
      query = query.eq('city_id', cityId);
    }
    
    if (featuredOnly) {
      query = query.eq('is_featured', true);
    }
    
    if (activeOnly) {
      query = query.eq('is_active', true);
    }
    
    const { data, error } = await query.order('name');
    
    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error fetching hotels:', error);
    return { data: null, error };
  }
};

export const getHotelById = async (id) => {
  try {
    const { data, error } = await supabase
      .from('hotels')
      .select(`
        *,
        cities (
          name,
          region,
          countries (
            name,
            code,
            currency
          )
        )
      `)
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error fetching hotel:', error);
    return { data: null, error };
  }
};

export const createHotel = async (hotelData) => {
  try {
    const { data, error } = await supabase
      .from('hotels')
      .insert([hotelData])
      .select()
      .single();
    
    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error creating hotel:', error);
    return { data: null, error };
  }
};

export const updateHotel = async (id, hotelData) => {
  try {
    const { data, error } = await supabase
      .from('hotels')
      .update(hotelData)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error updating hotel:', error);
    return { data: null, error };
  }
};

export const deleteHotel = async (id) => {
  try {
    const { error } = await supabase
      .from('hotels')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
    return { error: null };
  } catch (error) {
    console.error('Error deleting hotel:', error);
    return { error };
  }
};

// ===== CUSTOMERS =====
export const getCustomers = async () => {
  try {
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error fetching customers:', error);
    return { data: null, error };
  }
};

export const createCustomer = async (customerData) => {
  try {
    const { data, error } = await supabase
      .from('customers')
      .insert([customerData])
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error creating customer:', error);
    return { data: null, error };
  }
};

export const updateCustomer = async (id, customerData) => {
  try {
    const { data, error } = await supabase
      .from('customers')
      .update(customerData)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error updating customer:', error);
    return { data: null, error };
  }
};

export const deleteCustomer = async (id) => {
  try {
    const { error } = await supabase
      .from('customers')
      .delete()
      .eq('id', id);

    if (error) throw error;
    return { error: null };
  } catch (error) {
    console.error('Error deleting customer:', error);
    return { error };
  }
};

// ===== BOOKINGS =====
export const getBookings = async (customerId = null) => {
  try {
    let query = supabase
      .from('bookings')
      .select(`
        *,
        customers (
          first_name,
          last_name,
          email,
          phone
        ),
        hotels (
          name,
          stars,
          cities (
            name,
            countries (
              name
            )
          )
        )
      `);
    
    if (customerId) {
      query = query.eq('customer_id', customerId);
    }
    
    const { data, error } = await query.order('created_at', { ascending: false });
    
    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error fetching bookings:', error);
    return { data: null, error };
  }
};

export const createBooking = async (bookingData) => {
  try {
    // Generate booking reference
    const bookingReference = `BT${Date.now()}${Math.random().toString(36).substr(2, 4).toUpperCase()}`;
    
    const { data, error } = await supabase
      .from('bookings')
      .insert([{ ...bookingData, booking_reference: bookingReference }])
      .select()
      .single();
    
    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error creating booking:', error);
    return { data: null, error };
  }
};

export const updateBookingStatus = async (id, status) => {
  try {
    const { data, error } = await supabase
      .from('bookings')
      .update({ status })
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error updating booking status:', error);
    return { data: null, error };
  }
};

// ===== WEBSITE SETTINGS =====
export const getSettings = async () => {
  try {
    const { data, error } = await supabase
      .from('website_settings')
      .select('*');
    
    if (error) throw error;
    
    // Convert to key-value object
    const settings = {};
    data.forEach(setting => {
      settings[setting.key] = setting.value;
    });
    
    return { data: settings, error: null };
  } catch (error) {
    console.error('Error fetching settings:', error);
    return { data: null, error };
  }
};

export const updateSetting = async (key, value) => {
  try {
    const { data, error } = await supabase
      .from('website_settings')
      .upsert({ key, value })
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error updating setting:', error);
    return { data: null, error };
  }
};

// ===== EMAIL CAMPAIGNS =====
export const getEmailCampaigns = async () => {
  try {
    const { data, error } = await supabase
      .from('email_campaigns')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error fetching email campaigns:', error);
    return { data: null, error };
  }
};

export const createEmailCampaign = async (campaignData) => {
  try {
    const { data, error } = await supabase
      .from('email_campaigns')
      .insert([campaignData])
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error creating email campaign:', error);
    return { data: null, error };
  }
};

export const updateEmailCampaign = async (id, campaignData) => {
  try {
    const { data, error } = await supabase
      .from('email_campaigns')
      .update(campaignData)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error updating email campaign:', error);
    return { data: null, error };
  }
};

export const deleteEmailCampaign = async (id) => {
  try {
    const { error } = await supabase
      .from('email_campaigns')
      .delete()
      .eq('id', id);

    if (error) throw error;
    return { error: null };
  } catch (error) {
    console.error('Error deleting email campaign:', error);
    return { error };
  }
};

// ===== NEWSLETTER SUBSCRIBERS =====
export const getNewsletterSubscribers = async (activeOnly = true) => {
  try {
    let query = supabase
      .from('newsletter_subscribers')
      .select('*');

    if (activeOnly) {
      query = query.eq('is_active', true);
    }

    const { data, error } = await query.order('subscribed_at', { ascending: false });

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error fetching newsletter subscribers:', error);
    return { data: null, error };
  }
};

export const createNewsletterSubscriber = async (subscriberData) => {
  try {
    const { data, error } = await supabase
      .from('newsletter_subscribers')
      .insert([subscriberData])
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error creating newsletter subscriber:', error);
    return { data: null, error };
  }
};

export const updateNewsletterSubscriber = async (id, subscriberData) => {
  try {
    const { data, error } = await supabase
      .from('newsletter_subscribers')
      .update(subscriberData)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error updating newsletter subscriber:', error);
    return { data: null, error };
  }
};

export const unsubscribeNewsletter = async (email) => {
  try {
    const { data, error } = await supabase
      .from('newsletter_subscribers')
      .update({
        is_active: false,
        unsubscribed_at: new Date().toISOString()
      })
      .eq('email', email)
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error unsubscribing from newsletter:', error);
    return { data: null, error };
  }
};

// ===== ANALYTICS & REPORTS =====
export const getDashboardStats = async () => {
  try {
    const [
      { count: totalBookings },
      { count: totalCustomers },
      { count: totalHotels },
      { count: totalCountries },
      { count: pendingBookings },
      { count: confirmedBookings },
      { count: newsletterSubscribers }
    ] = await Promise.all([
      supabase.from('bookings').select('*', { count: 'exact', head: true }),
      supabase.from('customers').select('*', { count: 'exact', head: true }),
      supabase.from('hotels').select('*', { count: 'exact', head: true }).eq('is_active', true),
      supabase.from('countries').select('*', { count: 'exact', head: true }),
      supabase.from('bookings').select('*', { count: 'exact', head: true }).eq('status', 'pending'),
      supabase.from('bookings').select('*', { count: 'exact', head: true }).eq('status', 'confirmed'),
      supabase.from('newsletter_subscribers').select('*', { count: 'exact', head: true }).eq('is_active', true)
    ]);

    // Get recent bookings
    const { data: recentBookings, error: bookingsError } = await supabase
      .from('bookings')
      .select(`
        *,
        customers (first_name, last_name, email),
        hotels (name, cities (name, countries (name)))
      `)
      .order('created_at', { ascending: false })
      .limit(5);

    if (bookingsError) throw bookingsError;

    // Get revenue data (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const { data: revenueData, error: revenueError } = await supabase
      .from('bookings')
      .select('total_amount, currency, created_at')
      .gte('created_at', thirtyDaysAgo.toISOString())
      .eq('status', 'confirmed');

    if (revenueError) throw revenueError;

    const totalRevenue = revenueData?.reduce((sum, booking) => sum + (booking.total_amount || 0), 0) || 0;

    return {
      data: {
        totalBookings: totalBookings || 0,
        totalCustomers: totalCustomers || 0,
        totalHotels: totalHotels || 0,
        totalCountries: totalCountries || 0,
        pendingBookings: pendingBookings || 0,
        confirmedBookings: confirmedBookings || 0,
        newsletterSubscribers: newsletterSubscribers || 0,
        totalRevenue,
        recentBookings: recentBookings || [],
        revenueData: revenueData || []
      },
      error: null
    };
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return { data: null, error };
  }
};
