import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.supabaseUrl
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || process.env.supabaseKey

if (!supabaseUrl) {
  throw new Error(
    'Missing Supabase URL. Please set NEXT_PUBLIC_SUPABASE_URL in your .env file.\n' +
    'You can find this in your Supabase project settings under API.'
  )
}

if (!supabaseKey) {
  throw new Error(
    'Missing Supabase Anon Key. Please set NEXT_PUBLIC_SUPABASE_ANON_KEY in your .env file.\n' +
    'You can find this in your Supabase project settings under API.'
  )
}

// Create browser client for client-side operations
const supabase = createBrowserClient(supabaseUrl, supabaseKey)

export default supabase

export { supabaseUrl, supabaseKey }







