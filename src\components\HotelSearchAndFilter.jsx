"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { useToast } from '@/components/ui/Toast';
import { 
  Search, 
  Filter, 
  MapPin, 
  Star, 
  DollarSign, 
  Wifi,
  X,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

export default function HotelSearchAndFilter({ onResults, onFiltersChange, initialSearch = false }) {
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    country: '',
    city: '',
    minPrice: '',
    maxPrice: '',
    stars: '',
    amenities: [],
    sortBy: 'name',
    sortOrder: 'asc'
  });
  const [filterOptions, setFilterOptions] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const { toast } = useToast();

  // Fetch filter options on component mount
  useEffect(() => {
    fetchFilterOptions();
  }, []);

  // Perform initial search if requested
  useEffect(() => {
    if (initialSearch && filterOptions) {
      handleSearch();
    }
  }, [initialSearch, filterOptions]);

  const fetchFilterOptions = async () => {
    try {
      const response = await fetch('/api/hotels/filters');
      if (!response.ok) throw new Error('Failed to fetch filter options');
      
      const data = await response.json();
      setFilterOptions(data.data);
    } catch (error) {
      console.error('Error fetching filter options:', error);
      toast.error('Failed to load filter options');
    }
  };

  const handleSearch = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams();
      
      if (searchQuery) params.append('q', searchQuery);
      if (filters.country) params.append('country', filters.country);
      if (filters.city) params.append('city', filters.city);
      if (filters.minPrice) params.append('minPrice', filters.minPrice);
      if (filters.maxPrice) params.append('maxPrice', filters.maxPrice);
      if (filters.stars) params.append('stars', filters.stars);
      if (filters.amenities.length > 0) params.append('amenities', filters.amenities.join(','));
      params.append('sortBy', filters.sortBy);
      params.append('sortOrder', filters.sortOrder);

      const response = await fetch(`/api/hotels/search?${params}`);
      if (!response.ok) throw new Error('Search failed');
      
      const data = await response.json();
      onResults(data);
      onFiltersChange && onFiltersChange(filters);
    } catch (error) {
      console.error('Search error:', error);
      toast.error('Search failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    
    // If country changes, reset city
    if (key === 'country') {
      newFilters.city = '';
    }
    
    setFilters(newFilters);
  };

  const handleAmenityToggle = (amenity) => {
    const newAmenities = filters.amenities.includes(amenity)
      ? filters.amenities.filter(a => a !== amenity)
      : [...filters.amenities, amenity];
    
    handleFilterChange('amenities', newAmenities);
  };

  const clearFilters = () => {
    setFilters({
      country: '',
      city: '',
      minPrice: '',
      maxPrice: '',
      stars: '',
      amenities: [],
      sortBy: 'name',
      sortOrder: 'asc'
    });
    setSearchQuery('');
  };

  const getAvailableCities = () => {
    if (!filterOptions || !filters.country) return [];
    
    const selectedCountry = filterOptions.countries.find(c => c.name === filters.country);
    return selectedCountry ? selectedCountry.cities : [];
  };

  return (
    <Card className="mb-6">
      <CardContent className="p-6">
        <div className="space-y-6">
          {/* Main Search Bar */}
          <div className="flex gap-4">
            <div className="flex-1">
              <Input
                type="text"
                placeholder="Search hotels by name or description..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                icon={<Search className="w-4 h-4" />}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
            <Button
              onClick={handleSearch}
              disabled={isLoading}
              variant="primary"
              icon={<Search className="w-4 h-4" />}
            >
              {isLoading ? 'Searching...' : 'Search'}
            </Button>
            <Button
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              variant="outline"
              icon={showAdvancedFilters ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
            >
              Filters
            </Button>
          </div>

          {/* Advanced Filters */}
          {showAdvancedFilters && (
            <div className="border-t pt-6 space-y-4">
              {/* Location Filters */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <MapPin className="w-4 h-4 inline mr-1" />
                    Country
                  </label>
                  <select
                    value={filters.country}
                    onChange={(e) => handleFilterChange('country', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">All Countries</option>
                    {filterOptions?.countries.map(country => (
                      <option key={country.id} value={country.name}>
                        {country.name} ({country.cities.length} cities)
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    City
                  </label>
                  <select
                    value={filters.city}
                    onChange={(e) => handleFilterChange('city', e.target.value)}
                    disabled={!filters.country}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                  >
                    <option value="">All Cities</option>
                    {getAvailableCities().map(city => (
                      <option key={city.id} value={city.name}>
                        {city.name} ({city.hotelCount} hotels)
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Price and Star Rating */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <DollarSign className="w-4 h-4 inline mr-1" />
                    Min Price (DZD)
                  </label>
                  <input
                    type="number"
                    value={filters.minPrice}
                    onChange={(e) => handleFilterChange('minPrice', e.target.value)}
                    placeholder={`Min: ${filterOptions?.priceRange.min || 0}`}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Max Price (DZD)
                  </label>
                  <input
                    type="number"
                    value={filters.maxPrice}
                    onChange={(e) => handleFilterChange('maxPrice', e.target.value)}
                    placeholder={`Max: ${filterOptions?.priceRange.max || 1000}`}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Star className="w-4 h-4 inline mr-1" />
                    Star Rating
                  </label>
                  <select
                    value={filters.stars}
                    onChange={(e) => handleFilterChange('stars', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Any Rating</option>
                    {filterOptions?.starRatings.map(rating => (
                      <option key={rating.stars} value={rating.stars}>
                        {rating.stars} Star{rating.stars > 1 ? 's' : ''} ({rating.count} hotels)
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Amenities */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Wifi className="w-4 h-4 inline mr-1" />
                  Amenities
                </label>
                <div className="flex flex-wrap gap-2">
                  {filterOptions?.amenities.slice(0, 12).map(amenity => (
                    <button
                      key={amenity}
                      onClick={() => handleAmenityToggle(amenity)}
                      className={`px-3 py-1 rounded-full text-sm border transition-colors ${
                        filters.amenities.includes(amenity)
                          ? 'bg-blue-100 border-blue-300 text-blue-800'
                          : 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {amenity}
                    </button>
                  ))}
                </div>
                {filters.amenities.length > 0 && (
                  <div className="mt-2 text-sm text-gray-600">
                    Selected: {filters.amenities.join(', ')}
                  </div>
                )}
              </div>

              {/* Sort Options */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sort By
                  </label>
                  <select
                    value={filters.sortBy}
                    onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="name">Name</option>
                    <option value="price_per_night">Price</option>
                    <option value="stars">Star Rating</option>
                    <option value="created_at">Newest</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Order
                  </label>
                  <select
                    value={filters.sortOrder}
                    onChange={(e) => handleFilterChange('sortOrder', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="asc">Ascending</option>
                    <option value="desc">Descending</option>
                  </select>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4 border-t">
                <Button
                  onClick={handleSearch}
                  disabled={isLoading}
                  variant="primary"
                  icon={<Search className="w-4 h-4" />}
                >
                  Apply Filters
                </Button>
                <Button
                  onClick={clearFilters}
                  variant="outline"
                  icon={<X className="w-4 h-4" />}
                >
                  Clear All
                </Button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
