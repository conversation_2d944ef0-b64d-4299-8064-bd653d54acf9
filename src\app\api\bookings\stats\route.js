import { NextResponse } from 'next/server';
import { createServerSupabaseClient, requireAdmin } from '@/lib/auth';
import { BOOKING_STATUSES, PAYMENT_STATUSES } from '@/lib/booking-status-client';

export async function GET() {
  try {
    // Require admin authentication
    await requireAdmin();
    
    const supabase = await createServerSupabaseClient();
    
    const { data, error } = await supabase
      .from('bookings')
      .select('status, payment_status');

    if (error) {
      console.error('Error fetching booking stats:', error);
      return NextResponse.json(
        { error: 'Failed to fetch booking statistics' },
        { status: 500 }
      );
    }

    const stats = {
      total: data.length,
      byStatus: {},
      byPaymentStatus: {}
    };

    // Count by booking status
    Object.values(BOOKING_STATUSES).forEach(status => {
      stats.byStatus[status] = data.filter(b => b.status === status).length;
    });

    // Count by payment status
    Object.values(PAYMENT_STATUSES).forEach(status => {
      stats.byPaymentStatus[status] = data.filter(b => b.payment_status === status).length;
    });

    return NextResponse.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('API error:', error);
    
    if (error.message === 'Authentication required' || error.message === 'Admin privileges required') {
      return NextResponse.json(
        { error: error.message },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
