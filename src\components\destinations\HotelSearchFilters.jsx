"use client";
import { useState, useEffect } from "react";
import { Search, Filter, X, Star, MapPin, DollarSign } from "lucide-react";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import Badge from "@/components/ui/Badge";

export default function HotelSearchFilters({ 
  onSearch, 
  onFiltersChange, 
  loading = false,
  initialFilters = {}
}) {
  const [searchQuery, setSearchQuery] = useState(initialFilters.query || '');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    country: initialFilters.country || '',
    city: initialFilters.city || '',
    minPrice: initialFilters.minPrice || '',
    maxPrice: initialFilters.maxPrice || '',
    stars: initialFilters.stars || '',
    amenities: initialFilters.amenities || [],
    featured: initialFilters.featured || false,
    sortBy: initialFilters.sortBy || 'name',
    sortOrder: initialFilters.sortOrder || 'asc'
  });

  const [filterOptions, setFilterOptions] = useState({
    countries: [],
    priceRange: { min: 0, max: 1000 },
    amenities: [],
    starRatings: []
  });

  // Load filter options on mount
  useEffect(() => {
    loadFilterOptions();
  }, []);

  const loadFilterOptions = async () => {
    try {
      const response = await fetch('/api/hotels/filters');
      if (response.ok) {
        const data = await response.json();
        setFilterOptions(data.data);
      }
    } catch (error) {
      console.error('Error loading filter options:', error);
    }
  };

  const handleSearch = () => {
    onSearch(searchQuery, filters);
  };

  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handleAmenityToggle = (amenity) => {
    const newAmenities = filters.amenities.includes(amenity)
      ? filters.amenities.filter(a => a !== amenity)
      : [...filters.amenities, amenity];
    handleFilterChange('amenities', newAmenities);
  };

  const clearFilters = () => {
    const clearedFilters = {
      country: '',
      city: '',
      minPrice: '',
      maxPrice: '',
      stars: '',
      amenities: [],
      featured: false,
      sortBy: 'name',
      sortOrder: 'asc'
    };
    setFilters(clearedFilters);
    setSearchQuery('');
    onFiltersChange(clearedFilters);
    onSearch('', clearedFilters);
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.country) count++;
    if (filters.city) count++;
    if (filters.minPrice || filters.maxPrice) count++;
    if (filters.stars) count++;
    if (filters.amenities.length > 0) count++;
    if (filters.featured) count++;
    return count;
  };

  const selectedCountry = filterOptions.countries.find(c => c.id === filters.country);
  const availableCities = selectedCountry ? selectedCountry.cities : [];

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6 space-y-4">
      {/* Search Bar */}
      <div className="flex gap-4">
        <div className="flex-1">
          <Input
            type="text"
            placeholder="Rechercher des hôtels par nom, ville ou description..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            icon={<Search className="w-4 h-4" />}
            className="w-full"
          />
        </div>
        <Button
          onClick={handleSearch}
          variant="primary"
          loading={loading}
          className="px-6"
        >
          Rechercher
        </Button>
        <Button
          onClick={() => setShowFilters(!showFilters)}
          variant="outline"
          icon={<Filter className="w-4 h-4" />}
          className="relative"
        >
          Filtres
          {getActiveFiltersCount() > 0 && (
            <Badge 
              variant="primary" 
              size="sm" 
              className="absolute -top-2 -right-2 min-w-[20px] h-5 flex items-center justify-center text-xs"
            >
              {getActiveFiltersCount()}
            </Badge>
          )}
        </Button>
      </div>

      {/* Active Filters Display */}
      {getActiveFiltersCount() > 0 && (
        <div className="flex flex-wrap gap-2 items-center">
          <span className="text-sm text-gray-600">Filtres actifs:</span>
          {filters.country && (
            <Badge variant="outline" className="flex items-center gap-1">
              <MapPin className="w-3 h-3" />
              {filterOptions.countries.find(c => c.id === filters.country)?.name}
              <button onClick={() => handleFilterChange('country', '')}>
                <X className="w-3 h-3" />
              </button>
            </Badge>
          )}
          {filters.city && (
            <Badge variant="outline" className="flex items-center gap-1">
              {availableCities.find(c => c.id === filters.city)?.name}
              <button onClick={() => handleFilterChange('city', '')}>
                <X className="w-3 h-3" />
              </button>
            </Badge>
          )}
          {(filters.minPrice || filters.maxPrice) && (
            <Badge variant="outline" className="flex items-center gap-1">
              <DollarSign className="w-3 h-3" />
              {filters.minPrice || 0} - {filters.maxPrice || '∞'}
              <button onClick={() => {
                handleFilterChange('minPrice', '');
                handleFilterChange('maxPrice', '');
              }}>
                <X className="w-3 h-3" />
              </button>
            </Badge>
          )}
          {filters.stars && (
            <Badge variant="outline" className="flex items-center gap-1">
              <Star className="w-3 h-3" />
              {filters.stars} étoiles
              <button onClick={() => handleFilterChange('stars', '')}>
                <X className="w-3 h-3" />
              </button>
            </Badge>
          )}
          {filters.featured && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Vedettes
              <button onClick={() => handleFilterChange('featured', false)}>
                <X className="w-3 h-3" />
              </button>
            </Badge>
          )}
          {filters.amenities.length > 0 && (
            <Badge variant="outline" className="flex items-center gap-1">
              {filters.amenities.length} équipement(s)
              <button onClick={() => handleFilterChange('amenities', [])}>
                <X className="w-3 h-3" />
              </button>
            </Badge>
          )}
          <Button
            onClick={clearFilters}
            variant="ghost"
            size="sm"
            className="text-red-600 hover:text-red-700"
          >
            Effacer tout
          </Button>
        </div>
      )}

      {/* Expanded Filters */}
      {showFilters && (
        <div className="border-t pt-4 space-y-4 animate-fade-in">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Location Filters */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Pays
              </label>
              <select
                value={filters.country}
                onChange={(e) => {
                  handleFilterChange('country', e.target.value);
                  handleFilterChange('city', ''); // Reset city when country changes
                }}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
              >
                <option value="">Tous les pays</option>
                {filterOptions.countries.map(country => (
                  <option key={country.id} value={country.id}>
                    {country.name} ({country.cities.length})
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Ville
              </label>
              <select
                value={filters.city}
                onChange={(e) => handleFilterChange('city', e.target.value)}
                disabled={!filters.country}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary disabled:bg-gray-100"
              >
                <option value="">Toutes les villes</option>
                {availableCities.map(city => (
                  <option key={city.id} value={city.id}>
                    {city.name} ({city.hotelCount})
                  </option>
                ))}
              </select>
            </div>

            {/* Star Rating */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Étoiles
              </label>
              <select
                value={filters.stars}
                onChange={(e) => handleFilterChange('stars', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
              >
                <option value="">Toutes les étoiles</option>
                {filterOptions.starRatings.map(rating => (
                  <option key={rating.stars} value={rating.stars}>
                    {rating.stars} étoile{rating.stars > 1 ? 's' : ''} ({rating.count})
                  </option>
                ))}
              </select>
            </div>

            {/* Sort Options */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Trier par
              </label>
              <select
                value={`${filters.sortBy}-${filters.sortOrder}`}
                onChange={(e) => {
                  const [sortBy, sortOrder] = e.target.value.split('-');
                  handleFilterChange('sortBy', sortBy);
                  handleFilterChange('sortOrder', sortOrder);
                }}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
              >
                <option value="name-asc">Nom (A-Z)</option>
                <option value="name-desc">Nom (Z-A)</option>
                <option value="price-asc">Prix (croissant)</option>
                <option value="price-desc">Prix (décroissant)</option>
                <option value="stars-desc">Étoiles (décroissant)</option>
                <option value="stars-asc">Étoiles (croissant)</option>
              </select>
            </div>
          </div>

          {/* Price Range */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Fourchette de prix (par nuit)
            </label>
            <div className="flex gap-4 items-center">
              <Input
                type="number"
                placeholder="Min"
                value={filters.minPrice}
                onChange={(e) => handleFilterChange('minPrice', e.target.value)}
                className="w-24"
              />
              <span className="text-gray-500">-</span>
              <Input
                type="number"
                placeholder="Max"
                value={filters.maxPrice}
                onChange={(e) => handleFilterChange('maxPrice', e.target.value)}
                className="w-24"
              />
              <span className="text-sm text-gray-500">
                ({filterOptions.priceRange.min} - {filterOptions.priceRange.max})
              </span>
            </div>
          </div>

          {/* Featured Toggle */}
          <div>
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={filters.featured}
                onChange={(e) => handleFilterChange('featured', e.target.checked)}
                className="rounded border-gray-300"
              />
              <span className="text-sm font-medium text-gray-700">
                Hôtels vedettes uniquement
              </span>
            </label>
          </div>

          {/* Amenities */}
          {filterOptions.amenities.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Équipements
              </label>
              <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                {filterOptions.amenities.map(amenity => (
                  <button
                    key={amenity}
                    onClick={() => handleAmenityToggle(amenity)}
                    className={`px-3 py-1 rounded-full text-sm border transition-colors ${
                      filters.amenities.includes(amenity)
                        ? 'bg-primary text-white border-primary'
                        : 'bg-white text-gray-700 border-gray-300 hover:border-primary'
                    }`}
                  >
                    {amenity}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
