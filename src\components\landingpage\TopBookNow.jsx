"use client";
import { useEffect, useState } from "react";
import { getTopHotels } from "@/lib/database";
import {
  Card,
  CardContent,
  CardTitle,
  CardDescription,
} from "@/components/ui/Card";

export default function TopHotelsSection() {
  const [hotels, setHotels] = useState([]);

  useEffect(() => {
    async function fetchHotels() {
      try {
        const data = await getTopHotels();
        setHotels(data || []);
      } catch (err) {
        console.error("Error fetching hotels:", err);
      }
    }
    fetchHotels();
  }, []);

  return (
    <section className="px-4 py-12 md:px-16 bg-gray-50">
      <h2 className="text-3xl font-bold mb-10 text-center text-gray-800">
        🔥 Top Destinations
      </h2>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
        {hotels.map((hotel) => (
          <Card key={hotel.id} className="overflow-hidden shadow-md hover:shadow-xl transition-all rounded-2xl bg-white">
            {/* Image */}
            <div className="relative w-full h-48 bg-gray-200">
              <img
                src={hotel.image_url || "/placeholder.jpg"}
                alt={hotel.name}
                className="w-full h-full object-cover"
              />
              {/* Tags */}
              <div className="absolute top-2 left-2 flex gap-2 flex-wrap">
                {hotel.tags?.map((tag, i) => (
                  <span
                    key={i}
                    className="bg-sky-800 text-white text-xs font-semibold px-3 py-1 rounded-full"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>

            {/* Content */}
            <CardContent className="p-6 space-y-4">
              <CardTitle className="text-xl font-bold">{hotel.name}</CardTitle>
              <p className="text-gray-600 text-sm">{hotel.location}</p>

              <CardDescription className="text-sm text-gray-700">
                {hotel.description?.slice(0, 150)}...
              </CardDescription>

              {/* Interests */}
              {hotel.interests?.length > 0 && (
                <div>
                  <p className="font-semibold text-gray-800 text-sm">Points d'intérêt :</p>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {hotel.interests.map((item, i) => (
                      <span
                        key={i}
                        className="bg-white border border-gray-300 text-xs text-gray-800 px-3 py-1 rounded-full"
                      >
                        {item}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Period & Duration */}
              <div className="flex justify-between text-sm pt-4 text-gray-600">
                <div>
                  <span className="font-semibold">Période :</span><br />
                  {hotel.period || "N/A"}
                </div>
                <div>
                  <span className="font-semibold">Durée :</span><br />
                  {hotel.duration || "N/A"}
                </div>
              </div>

              {/* CTA */}
              <div className="pt-4">
                <button className="bg-sky-800 text-white text-sm font-semibold px-5 py-2 rounded hover:bg-sky-700 transition-all">
                  DÉCOUVRIR
                </button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </section>
  );
}
