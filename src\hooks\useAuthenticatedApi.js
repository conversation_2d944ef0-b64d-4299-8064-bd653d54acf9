"use client";

import { useSession } from "@/components/providers/SessionProvider";
import { useCallback } from "react";

/**
 * Custom hook for making authenticated API calls with automatic token refresh
 * This hook ensures that API calls work reliably even when tokens are close to expiring
 */
export function useAuthenticatedApi() {
  const { session, refreshSession, supabase } = useSession();

  const makeAuthenticatedRequest = useCallback(async (requestFn) => {
    // Check if session exists
    if (!session) {
      throw new Error('No active session');
    }

    // Check if token is close to expiring (within 10 minutes)
    if (session.expires_at) {
      const expiresAt = new Date(session.expires_at * 1000);
      const now = new Date();
      const timeUntilExpiry = expiresAt.getTime() - now.getTime();
      const tenMinutes = 10 * 60 * 1000; // 10 minutes in milliseconds

      if (timeUntilExpiry < tenMinutes && timeUntilExpiry > 0) {
        console.log('API call: Token expires soon, refreshing...');
        const refreshSuccess = await refreshSession();
        if (!refreshSuccess) {
          throw new Error('Failed to refresh session');
        }
      }
    }

    // Make the API request
    try {
      return await requestFn();
    } catch (error) {
      // If we get an auth error, try refreshing the token once
      if (error.message?.includes('JWT') || error.message?.includes('auth') || error.status === 401) {
        console.log('API call: Auth error detected, attempting token refresh...');
        const refreshSuccess = await refreshSession();
        if (refreshSuccess) {
          // Retry the request with the new token
          return await requestFn();
        } else {
          throw new Error('Authentication failed - please sign in again');
        }
      }
      throw error;
    }
  }, [session, refreshSession]);

  const authenticatedSupabase = useCallback(() => {
    if (!session) {
      throw new Error('No active session');
    }
    return supabase;
  }, [session, supabase]);

  return {
    makeAuthenticatedRequest,
    authenticatedSupabase,
    isAuthenticated: !!session
  };
}

/**
 * Wrapper for database operations with automatic token refresh
 */
export function useAuthenticatedDatabase() {
  const { makeAuthenticatedRequest, authenticatedSupabase } = useAuthenticatedApi();

  const safeDbOperation = useCallback(async (operation) => {
    return makeAuthenticatedRequest(async () => {
      const supabase = authenticatedSupabase();
      return await operation(supabase);
    });
  }, [makeAuthenticatedRequest, authenticatedSupabase]);

  return {
    safeDbOperation
  };
}
