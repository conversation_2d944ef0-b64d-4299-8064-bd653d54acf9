"use client";
import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import { useToast } from "@/components/ui/Toast";
import { PageLoading } from "@/components/ui/LoadingSpinner";
import { Search, Filter, Download, Eye, Edit } from "lucide-react";
import { getCustomers } from "@/lib/database";
import {
  getBookingsByStatusClient,
  updateBookingStatusClient,
  updatePaymentStatusClient,
  getBookingStatusStatsClient,
  BOOKING_STATUSES
} from "@/lib/booking-status-client";
import BookingStatusManager from "@/components/admin/BookingStatusManager";
import BookingModificationModal from "@/components/admin/BookingModificationModal";

export default function BookingsAndCustomers() {
  const [activeTab, setActiveTab] = useState("bookings");
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [bookings, setBookings] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [statusStats, setStatusStats] = useState(null);
  const [showModificationModal, setShowModificationModal] = useState(false);
  const [bookingToModify, setBookingToModify] = useState(null);
  const { toast } = useToast();

  useEffect(() => {
    fetchData();
  }, [statusFilter]);

  // Handle booking status update
  const handleStatusUpdate = async (bookingId, newStatus, notes) => {
    setIsUpdatingStatus(true);
    try {
      const { error } = await updateBookingStatusClient(bookingId, newStatus, notes);
      if (error) throw error;

      // Refresh bookings data
      await fetchData();
      toast.success('Booking status updated successfully!');
    } catch (error) {
      console.error('Error updating booking status:', error);
      throw error;
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  // Handle payment status update
  const handlePaymentStatusUpdate = async (bookingId, newPaymentStatus, notes) => {
    setIsUpdatingStatus(true);
    try {
      const { error } = await updatePaymentStatusClient(bookingId, newPaymentStatus, notes);
      if (error) throw error;

      // Refresh bookings data
      await fetchData();
      toast.success('Payment status updated successfully!');
    } catch (error) {
      console.error('Error updating payment status:', error);
      throw error;
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  // Handle booking modification
  const handleModifyBooking = (booking) => {
    setBookingToModify(booking);
    setShowModificationModal(true);
  };

  // Handle booking update from modal
  const handleBookingUpdate = async (updatedBooking) => {
    // Refresh bookings data
    await fetchData();
    toast.success('Booking updated successfully!');
  };

  // Handle booking cancellation from modal
  const handleBookingCancel = async (cancelledBooking) => {
    // Refresh bookings data
    await fetchData();
    toast.success('Booking cancelled successfully!');
  };

  const fetchData = async () => {
    setLoading(true);
    setError(null);

    try {
      const [bookingsRes, customersRes, statsRes] = await Promise.all([
        getBookingsByStatusClient(statusFilter),
        getCustomers(),
        getBookingStatusStatsClient()
      ]);

      if (bookingsRes.error) {
        console.error('Error fetching bookings:', bookingsRes.error);
        toast.error(`Erreur lors du chargement des réservations: ${bookingsRes.error.message}`);
        setError(`Erreur lors du chargement des réservations: ${bookingsRes.error.message}`);
        setBookings([]); // Ensure bookings is always an array
      } else {
        // Ensure bookings data is always an array
        const bookingsData = Array.isArray(bookingsRes.data) ? bookingsRes.data : [];
        setBookings(bookingsData);
      }

      if (customersRes.error) {
        console.error('Error fetching customers:', customersRes.error);
        toast.error(`Erreur lors du chargement des clients: ${customersRes.error.message}`);
        setError(`Erreur lors du chargement des clients: ${customersRes.error.message}`);
        setCustomers([]); // Ensure customers is always an array
      } else {
        // Ensure customers data is always an array
        const customersData = Array.isArray(customersRes.data) ? customersRes.data : [];
        setCustomers(customersData);
      }

      if (statsRes.error) {
        console.error('Error fetching stats:', statsRes.error);
        toast.warning('Impossible de charger les statistiques');
      } else {
        setStatusStats(statsRes.data);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      const errorMessage = `Erreur de connexion: ${error.message}`;
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Ensure bookings and customers are arrays before filtering
  const bookingsArray = Array.isArray(bookings) ? bookings : [];
  const customersArray = Array.isArray(customers) ? customers : [];

  const filteredBookings = bookingsArray.filter((booking) => {
    const matchesSearch =
      booking.customers?.first_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      booking.customers?.last_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      booking.customers?.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      booking.hotels?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      booking.booking_reference?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = statusFilter === "all" || booking.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const filteredCustomers = customersArray.filter((customer) =>
    customer.first_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    customer.last_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    customer.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    customer.phone?.includes(searchQuery)
  );

  if (loading) {
    return <PageLoading message="Chargement des réservations et clients..." />;
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="mb-6">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-red-500 text-2xl">⚠️</span>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Erreur de chargement</h2>
            <p className="text-gray-600">{error}</p>
          </div>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button
              onClick={fetchData}
              variant="primary"
              disabled={loading}
            >
              {loading ? 'Chargement...' : 'Réessayer'}
            </Button>
            <Button
              onClick={() => window.location.href = '/admin'}
              variant="outline"
            >
              Retour au tableau de bord
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container-custom">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Bookings & Customers
          </h1>
          <Button variant="outline" icon={<Download className="w-4 h-4" />}>
            Export Data
          </Button>
        </div>

        {/* Tabs */}
        <div className="flex space-x-4 mb-6">
          <button
            className={`px-4 py-2 rounded-lg font-medium ${
              activeTab === "bookings"
                ? "bg-primary text-white"
                : "bg-white text-gray-600 hover:bg-gray-50"
            }`}
            onClick={() => setActiveTab("bookings")}
          >
            Bookings
          </button>
          <button
            className={`px-4 py-2 rounded-lg font-medium ${
              activeTab === "customers"
                ? "bg-primary text-white"
                : "bg-white text-gray-600 hover:bg-gray-50"
            }`}
            onClick={() => setActiveTab("customers")}
          >
            Customers
          </button>
        </div>

        {/* Search and Filter */}
        <div className="flex gap-4 mb-6">
          <div className="flex-1">
            <Input
              type="text"
              placeholder={`Search ${activeTab}...`}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              icon={<Search className="w-4 h-4" />}
            />
          </div>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
          >
            <option value="all">All Status ({statusStats?.total || 0})</option>
            <option value={BOOKING_STATUSES.PENDING}>
              Pending ({statusStats?.byStatus?.[BOOKING_STATUSES.PENDING] || 0})
            </option>
            <option value={BOOKING_STATUSES.CONFIRMED}>
              Confirmed ({statusStats?.byStatus?.[BOOKING_STATUSES.CONFIRMED] || 0})
            </option>
            <option value={BOOKING_STATUSES.COMPLETED}>
              Completed ({statusStats?.byStatus?.[BOOKING_STATUSES.COMPLETED] || 0})
            </option>
            <option value={BOOKING_STATUSES.CANCELLED}>
              Cancelled ({statusStats?.byStatus?.[BOOKING_STATUSES.CANCELLED] || 0})
            </option>
          </select>
        </div>

        {/* Content */}
        <div className="grid gap-6">
          {activeTab === "bookings"
            ? // Bookings List
              filteredBookings.map((booking) => (
                <Card key={booking.id}>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          {booking.hotels?.name || 'Hôtel non spécifié'}
                        </h3>
                        <p className="text-gray-600">
                          {booking.customers?.first_name} {booking.customers?.last_name}
                        </p>
                        <p className="text-sm text-gray-500">{booking.customers?.email}</p>
                        <p className="text-xs text-gray-400">Réf: {booking.booking_reference}</p>
                        <div className="mt-2 flex gap-4">
                          <span className="text-sm">
                            Arrivée: {new Date(booking.check_in_date).toLocaleDateString()}
                          </span>
                          <span className="text-sm">
                            Départ: {new Date(booking.check_out_date).toLocaleDateString()}
                          </span>
                        </div>
                        <div className="mt-1 flex gap-4 text-xs text-gray-500">
                          <span>{booking.adults} adulte(s)</span>
                          {booking.children > 0 && <span>{booking.children} enfant(s)</span>}
                          <span>{booking.rooms} chambre(s)</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <span
                          className={`px-3 py-1 rounded-full text-sm ${
                            booking.status === "confirmed"
                              ? "bg-green-100 text-green-800"
                              : booking.status === "pending"
                              ? "bg-yellow-100 text-yellow-800"
                              : booking.status === "cancelled"
                              ? "bg-red-100 text-red-800"
                              : "bg-blue-100 text-blue-800"
                          }`}
                        >
                          {booking.status === "confirmed" ? "Confirmé" :
                           booking.status === "pending" ? "En attente" :
                           booking.status === "cancelled" ? "Annulé" :
                           booking.status === "completed" ? "Terminé" : booking.status}
                        </span>
                        <p className="mt-2 text-lg font-bold text-primary">
                          {booking.total_amount?.toLocaleString()} {booking.currency}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          Paiement: {booking.payment_status === "paid" ? "Payé" :
                                   booking.payment_status === "pending" ? "En attente" :
                                   booking.payment_status === "failed" ? "Échoué" :
                                   booking.payment_status === "refunded" ? "Remboursé" : booking.payment_status}
                        </p>
                        <div className="mt-3 flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            icon={<Eye className="w-4 h-4" />}
                            onClick={() => setSelectedBooking(selectedBooking?.id === booking.id ? null : booking)}
                          >
                            {selectedBooking?.id === booking.id ? 'Hide Details' : 'Manage Status'}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            icon={<Edit className="w-4 h-4" />}
                            onClick={() => handleModifyBooking(booking)}
                          >
                            Modify
                          </Button>
                        </div>
                      </div>
                    </div>

                    {/* Booking Status Manager */}
                    {selectedBooking?.id === booking.id && (
                      <div className="mt-6 pt-6 border-t border-gray-200">
                        <BookingStatusManager
                          booking={booking}
                          onStatusUpdate={handleStatusUpdate}
                          onPaymentStatusUpdate={handlePaymentStatusUpdate}
                          isUpdating={isUpdatingStatus}
                        />
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))
            : // Customers List
              filteredCustomers.map((customer) => (
                <Card key={customer.id}>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          {customer.first_name} {customer.last_name}
                        </h3>
                        <p className="text-gray-600">{customer.email}</p>
                        {customer.phone && (
                          <p className="text-sm text-gray-500">{customer.phone}</p>
                        )}
                        {(customer.city || customer.country) && (
                          <p className="text-sm text-gray-500">
                            {[customer.city, customer.country].filter(Boolean).join(', ')}
                          </p>
                        )}  
                        <div className="mt-2 flex gap-4">
                          <span className="text-sm">
                            Inscrit: {new Date(customer.created_at).toLocaleDateString()}
                          </span>
                          {customer.is_newsletter_subscribed && (
                            <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                              Newsletter
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          icon={<Eye className="w-4 h-4" />}
                        >
                          Voir Détails
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
        </div>
      </div>

      {/* Booking Modification Modal */}
      <BookingModificationModal
        booking={bookingToModify}
        isOpen={showModificationModal}
        onClose={() => {
          setShowModificationModal(false);
          setBookingToModify(null);
        }}
        onUpdate={handleBookingUpdate}
        onCancel={handleBookingCancel}
      />
    </div>
  );
}
