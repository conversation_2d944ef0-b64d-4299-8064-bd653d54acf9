-- Cloudinary Image Management Migration
-- Run this in your Supabase SQL Editor

-- Create images table for centralized image management
CREATE TABLE IF NOT EXISTS images (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  cloudinary_public_id VARCHAR(255) UNIQUE NOT NULL,
  cloudinary_url TEXT NOT NULL,
  original_filename VARCHAR(255),
  alt_text TEXT,
  caption TEXT,
  width INTEGER,
  height INTEGER,
  format VARCHAR(10),
  bytes INTEGER,
  folder VARCHAR(100) DEFAULT 'hotels',
  tags TEXT[] DEFAULT ARRAY[]::TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create hotel_images junction table for many-to-many relationship
CREATE TABLE IF NOT EXISTS hotel_images (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  hotel_id UUID REFERENCES hotels(id) ON DELETE CASCADE,
  image_id UUID REFERENCES images(id) ON DELETE CASCADE,
  image_type VARCHAR(20) DEFAULT 'gallery', -- 'primary', 'gallery', 'thumbnail'
  display_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(hotel_id, image_id)
);

-- Add Cloudinary fields to existing hotels table
ALTER TABLE hotels ADD COLUMN IF NOT EXISTS primary_image_public_id VARCHAR(255);
ALTER TABLE hotels ADD COLUMN IF NOT EXISTS primary_image_url TEXT;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_images_public_id ON images(cloudinary_public_id);
CREATE INDEX IF NOT EXISTS idx_images_folder ON images(folder);
CREATE INDEX IF NOT EXISTS idx_hotel_images_hotel_id ON hotel_images(hotel_id);
CREATE INDEX IF NOT EXISTS idx_hotel_images_type ON hotel_images(image_type);
CREATE INDEX IF NOT EXISTS idx_hotel_images_order ON hotel_images(hotel_id, display_order);

-- Create function to get hotel with images
CREATE OR REPLACE FUNCTION get_hotel_with_images(hotel_uuid UUID)
RETURNS TABLE (
  hotel_data JSONB,
  primary_image JSONB,
  gallery_images JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    -- Hotel data
    to_jsonb(h.*) as hotel_data,
    
    -- Primary image
    CASE 
      WHEN h.primary_image_public_id IS NOT NULL THEN
        jsonb_build_object(
          'public_id', h.primary_image_public_id,
          'url', h.primary_image_url,
          'alt', COALESCE(pi.alt_text, h.name || ' - Image principale'),
          'width', pi.width,
          'height', pi.height
        )
      ELSE NULL
    END as primary_image,
    
    -- Gallery images
    COALESCE(
      (SELECT jsonb_agg(
        jsonb_build_object(
          'id', hi.id,
          'public_id', i.cloudinary_public_id,
          'url', i.cloudinary_url,
          'alt', COALESCE(i.alt_text, h.name || ' - Image ' || hi.display_order),
          'caption', i.caption,
          'width', i.width,
          'height', i.height,
          'display_order', hi.display_order,
          'image_type', hi.image_type
        ) ORDER BY hi.display_order, hi.created_at
      ) 
      FROM hotel_images hi
      JOIN images i ON hi.image_id = i.id
      WHERE hi.hotel_id = hotel_uuid 
        AND hi.is_active = true
        AND hi.image_type = 'gallery'
      ),
      '[]'::jsonb
    ) as gallery_images
    
  FROM hotels h
  LEFT JOIN images pi ON h.primary_image_public_id = pi.cloudinary_public_id
  WHERE h.id = hotel_uuid;
END;
$$ LANGUAGE plpgsql;

-- Create function to update hotel image order
CREATE OR REPLACE FUNCTION update_hotel_image_order(
  hotel_uuid UUID,
  image_orders JSONB
)
RETURNS BOOLEAN AS $$
DECLARE
  image_order JSONB;
BEGIN
  -- Loop through the image orders and update
  FOR image_order IN SELECT * FROM jsonb_array_elements(image_orders)
  LOOP
    UPDATE hotel_images 
    SET 
      display_order = (image_order->>'order')::INTEGER,
      updated_at = NOW()
    WHERE hotel_id = hotel_uuid 
      AND id = (image_order->>'id')::UUID;
  END LOOP;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Create function to add image to hotel
CREATE OR REPLACE FUNCTION add_image_to_hotel(
  hotel_uuid UUID,
  image_public_id VARCHAR(255),
  image_url TEXT,
  image_alt TEXT DEFAULT NULL,
  image_caption TEXT DEFAULT NULL,
  image_width INTEGER DEFAULT NULL,
  image_height INTEGER DEFAULT NULL,
  image_format VARCHAR(10) DEFAULT NULL,
  image_bytes INTEGER DEFAULT NULL,
  image_type VARCHAR(20) DEFAULT 'gallery'
)
RETURNS UUID AS $$
DECLARE
  image_uuid UUID;
  hotel_image_uuid UUID;
  max_order INTEGER;
BEGIN
  -- Insert or get existing image
  INSERT INTO images (
    cloudinary_public_id,
    cloudinary_url,
    alt_text,
    caption,
    width,
    height,
    format,
    bytes,
    folder,
    tags
  ) VALUES (
    image_public_id,
    image_url,
    image_alt,
    image_caption,
    image_width,
    image_height,
    image_format,
    image_bytes,
    'hotels',
    ARRAY['hotel', 'travel']
  )
  ON CONFLICT (cloudinary_public_id) 
  DO UPDATE SET
    cloudinary_url = EXCLUDED.cloudinary_url,
    alt_text = COALESCE(EXCLUDED.alt_text, images.alt_text),
    caption = COALESCE(EXCLUDED.caption, images.caption),
    updated_at = NOW()
  RETURNING id INTO image_uuid;

  -- Get the next display order
  SELECT COALESCE(MAX(display_order), 0) + 1 
  INTO max_order
  FROM hotel_images 
  WHERE hotel_id = hotel_uuid AND image_type = image_type;

  -- Link image to hotel
  INSERT INTO hotel_images (
    hotel_id,
    image_id,
    image_type,
    display_order
  ) VALUES (
    hotel_uuid,
    image_uuid,
    image_type,
    max_order
  )
  ON CONFLICT (hotel_id, image_id)
  DO UPDATE SET
    image_type = EXCLUDED.image_type,
    display_order = EXCLUDED.display_order,
    is_active = true,
    updated_at = NOW()
  RETURNING id INTO hotel_image_uuid;

  RETURN hotel_image_uuid;
END;
$$ LANGUAGE plpgsql;

-- Create function to remove image from hotel
CREATE OR REPLACE FUNCTION remove_image_from_hotel(
  hotel_uuid UUID,
  image_public_id VARCHAR(255)
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Soft delete the hotel-image relationship
  UPDATE hotel_images 
  SET 
    is_active = false,
    updated_at = NOW()
  WHERE hotel_id = hotel_uuid 
    AND image_id IN (
      SELECT id FROM images WHERE cloudinary_public_id = image_public_id
    );
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply triggers
DROP TRIGGER IF EXISTS update_images_updated_at ON images;
CREATE TRIGGER update_images_updated_at
  BEFORE UPDATE ON images
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_hotel_images_updated_at ON hotel_images;
CREATE TRIGGER update_hotel_images_updated_at
  BEFORE UPDATE ON hotel_images
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions (adjust based on your RLS policies)
-- These are examples - modify according to your security requirements

-- Enable RLS
ALTER TABLE images ENABLE ROW LEVEL SECURITY;
ALTER TABLE hotel_images ENABLE ROW LEVEL SECURITY;

-- Create policies for images (allow all operations for now - customize as needed)
CREATE POLICY "Allow all operations on images" ON images FOR ALL USING (true);
CREATE POLICY "Allow all operations on hotel_images" ON hotel_images FOR ALL USING (true);
