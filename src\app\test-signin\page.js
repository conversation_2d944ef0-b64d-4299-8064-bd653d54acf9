"use client";

import { useState } from "react";
import { createBrowserClient } from '@supabase/ssr'

export default function TestSignIn() {
  const [result, setResult] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Create Supabase client
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  );

  const testSignIn = async () => {
    setIsLoading(true);
    setResult("Testing signin...");

    try {
      // Test signin
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email: "<EMAIL>",
        password: "123456", // You'll need to provide the correct password
      });

      if (authError) {
        setResult(`Auth Error: ${authError.message}`);
        return;
      }

      setResult(`Auth Success: User ID ${authData.user.id}`);

      // Test profile fetch
      const { data: profile, error: profileError } = await supabase
        .from("profiles")
        .select("role")
        .eq("user_id", authData.user.id)
        .single();

      if (profileError) {
        setResult(prev => prev + `\nProfile Error: ${profileError.message}`);
        return;
      }

      setResult(prev => prev + `\nProfile Success: Role ${profile.role}`);

    } catch (error) {
      setResult(`Unexpected Error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testSession = async () => {
    setIsLoading(true);
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error) {
        setResult(`Session Error: ${error.message}`);
      } else if (session) {
        setResult(`Session exists: User ${session.user.email}, Expires: ${new Date(session.expires_at * 1000)}`);
      } else {
        setResult("No active session");
      }
    } catch (error) {
      setResult(`Session check error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async () => {
    setIsLoading(true);
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        setResult(`Sign out error: ${error.message}`);
      } else {
        setResult("Signed out successfully");
      }
    } catch (error) {
      setResult(`Sign out error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="w-full max-w-md p-8 bg-white rounded-lg shadow-lg">
        <h2 className="text-2xl font-bold text-center mb-6">Test Authentication</h2>
        
        <div className="space-y-4">
          <button
            onClick={testSignIn}
            disabled={isLoading}
            className="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-semibold rounded"
          >
            {isLoading ? 'Testing...' : 'Test Sign In'}
          </button>

          <button
            onClick={testSession}
            disabled={isLoading}
            className="w-full py-2 px-4 bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white font-semibold rounded"
          >
            {isLoading ? 'Testing...' : 'Check Session'}
          </button>

          <button
            onClick={signOut}
            disabled={isLoading}
            className="w-full py-2 px-4 bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white font-semibold rounded"
          >
            {isLoading ? 'Testing...' : 'Sign Out'}
          </button>
        </div>

        {result && (
          <div className="mt-6 p-4 bg-gray-100 rounded">
            <pre className="text-sm whitespace-pre-wrap">{result}</pre>
          </div>
        )}
      </div>
    </div>
  );
}
