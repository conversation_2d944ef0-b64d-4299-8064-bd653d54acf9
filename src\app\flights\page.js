"use client";
import { useEffect, useState } from "react";
import { PlaneTakeoff, PlaneLanding, Clock, DollarSign } from "lucide-react";
import Header from "@/components/header/Header";
import Footer from "@/components/footer/Footer";
export default function Home() {
  const [flights, setFlights] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchFlights = async () => {
      try {
        const res = await fetch("/api/flights");
        if (!res.ok) throw new Error("Failed to fetch flights");
        const data = await res.json();
        setFlights(data.data);
      } catch (err) {
        setError(err.message);
      }
    };

    fetchFlights();
  }, []);

  if (error)
    return <div className="p-6 text-center text-red-600 font-semibold">❌ Error: {error}</div>;

  if (!flights)
    return <div className="p-6 text-center text-gray-600 animate-pulse">⏳ Loading flights...</div>;

  return (
    <div>
     <Header/>   
    <main className="min-h-screen bg-gradient-to-br from-blue-50 to-white py-10 px-6 pt-24">
          
      <h1 className="text-3xl font-bold text-center mb-10 text-blue-900">
        COMING SOOOOOOOOOOOOON
      </h1>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto">
        {flights.map((flight, index) => {
          const segments = flight.itineraries[0].segments;
          const departure = segments[0].departure;
          const arrival = segments.at(-1).arrival;
          return (
            <div
              key={index}
              className="bg-white shadow-lg rounded-2xl p-5 border border-blue-100 hover:scale-[1.02] transition"
            >
              <div className="flex items-center gap-2 mb-2 text-blue-800">
                <PlaneTakeoff className="w-5 h-5" />
                <span className="font-medium">
                  {departure.iataCode} → {arrival.iataCode}
                </span>
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
                <Clock className="w-4 h-4" />
                Departure:{" "}
                <span className="font-semibold text-gray-800 ml-1">{departure.at}</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600 mb-3">
                <PlaneLanding className="w-4 h-4" />
                Arrival:{" "}
                <span className="font-semibold text-gray-800 ml-1">{arrival.at}</span>
              </div>
              <div className="flex items-center justify-between mt-auto pt-2 border-t border-blue-50">
                <span className="text-blue-600 font-semibold text-lg flex items-center gap-1">
                  <DollarSign className="w-4 h-4" />
                  {flight.price.total} {flight.price.currency}
                </span>
              </div>
            </div>
          );
        })}
      </div>
    </main>
    <Footer/>
    </div>
  );
}
