import supabase from"@/Config/supabase";import{validateEnvironment}from"./env-check";const envCheck=validateEnvironment();envCheck.isValid||(console.error("❌ Supabase configuration error:"),envCheck.report());export const getCountries=async(featuredOnly=!1)=>{try{let query=supabase.from("countries").select("*");featuredOnly&&(query=query.eq("is_featured",!0));const{data:data,error:error}=await query.order("name");if(error)throw error;return{data:data,error:null}}catch(error){return console.error("Error fetching countries:",error),{data:null,error:error}}};export async function getTopHotels(){const{data:data,error:error}=await supabase.from("hotels").select("*").eq("top_book_now",!0).eq("is_active",!0);return error?(console.error("Supabase error:",error),[]):(console.log("✅ getTopHotels returned:",data),data)}export async function toggleTopBookNow(hotelId,currentValue){const{data:data,error:error}=await supabase.from("hotels").update({top_book_now:!currentValue}).eq("id",hotelId);if(error)throw console.error("Toggle error:",error.message),error;return data}export const createCountry=async countryData=>{try{const{data:data,error:error}=await supabase.from("countries").insert([countryData]).select().single();if(error)throw error;return{data:data,error:null}}catch(error){return console.error("Error creating country:",error),{data:null,error:error}}};export const updateCountry=async(id,countryData)=>{try{const{data:data,error:error}=await supabase.from("countries").update(countryData).eq("id",id).select().single();if(error)throw error;return{data:data,error:null}}catch(error){return console.error("Error updating country:",error),{data:null,error:error}}};export const getCities=async(countryId=null,featuredOnly=!1)=>{try{let query=supabase.from("cities").select("\n        *,\n        countries (\n          name,\n          code,\n          currency,\n          is_featured\n        )\n      ");countryId&&(query=query.eq("country_id",countryId)),featuredOnly&&(query=query.eq("is_featured",!0));const{data:data,error:error}=await query.order("name");if(error)throw error;return{data:data,error:null}}catch(error){return console.error("Error fetching cities:",error),{data:null,error:error}}};export const getCityById=async id=>{try{const{data:data,error:error}=await supabase.from("cities").select("\n        *,\n        countries (\n          name,\n          code,\n          currency,\n          is_featured\n        )\n      ").eq("id",id).single();if(error)throw error;return{data:data,error:null}}catch(error){return console.error("Error fetching city:",error),{data:null,error:error}}};export const createCity=async cityData=>{try{const{data:data,error:error}=await supabase.from("cities").insert([cityData]).select().single();if(error)throw error;return{data:data,error:null}}catch(error){return console.error("Error creating city:",error),{data:null,error:error}}};export const updateCity=async(id,cityData)=>{try{const{data:data,error:error}=await supabase.from("cities").update(cityData).eq("id",id).select().single();if(error)throw error;return{data:data,error:null}}catch(error){return console.error("Error updating city:",error),{data:null,error:error}}};export const getHotels=async(cityId=null,featuredOnly=!1,activeOnly=!0)=>{try{let query=supabase.from("hotels").select("\n        *,\n        cities (\n          name,\n          region,\n          countries (\n            name,\n            code,\n            currency\n          )\n        )\n      ");cityId&&(query=query.eq("city_id",cityId)),featuredOnly&&(query=query.eq("is_featured",!0)),activeOnly&&(query=query.eq("is_active",!0));const{data:data,error:error}=await query.order("name");if(error)throw error;return{data:data,error:null}}catch(error){return console.error("Error fetching hotels:",error),{data:null,error:error}}};export const getHotelById=async id=>{try{const{data:data,error:error}=await supabase.from("hotels").select("\n        *,\n        cities (\n          name,\n          region,\n          countries (\n            name,\n            code,\n            currency\n          )\n        )\n      ").eq("id",id).single();if(error)throw error;return{data:data,error:null}}catch(error){return console.error("Error fetching hotel:",error),{data:null,error:error}}};export const createHotel=async hotelData=>{try{const{data:data,error:error}=await supabase.from("hotels").insert([hotelData]).select().single();if(error)throw error;return{data:data,error:null}}catch(error){return console.error("Error creating hotel:",error),{data:null,error:error}}};export const updateHotel=async(id,hotelData)=>{try{const{data:data,error:error}=await supabase.from("hotels").update(hotelData).eq("id",id).select().single();if(error)throw error;return{data:data,error:null}}catch(error){return console.error("Error updating hotel:",error),{data:null,error:error}}};export const deleteHotel=async id=>{try{const{error:error}=await supabase.from("hotels").delete().eq("id",id);if(error)throw error;return{error:null}}catch(error){return console.error("Error deleting hotel:",error),{error:error}}};export const getCustomers=async()=>{try{const{data:data,error:error}=await supabase.from("customers").select("*").order("created_at",{ascending:!1});if(error)throw error;return{data:data,error:null}}catch(error){return console.error("Error fetching customers:",error),{data:null,error:error}}};export const createCustomer=async customerData=>{try{const{data:data,error:error}=await supabase.from("customers").insert([customerData]).select().single();if(error)throw error;return{data:data,error:null}}catch(error){return console.error("Error creating customer:",error),{data:null,error:error}}};export const updateCustomer=async(id,customerData)=>{try{const{data:data,error:error}=await supabase.from("customers").update(customerData).eq("id",id).select().single();if(error)throw error;return{data:data,error:null}}catch(error){return console.error("Error updating customer:",error),{data:null,error:error}}};export const deleteCustomer=async id=>{try{const{error:error}=await supabase.from("customers").delete().eq("id",id);if(error)throw error;return{error:null}}catch(error){return console.error("Error deleting customer:",error),{error:error}}};export const getBookings=async(customerId=null)=>{try{let query=supabase.from("bookings").select("\n        *,\n        customers (\n          first_name,\n          last_name,\n          email,\n          phone\n        ),\n        hotels (\n          name,\n          stars,\n          cities (\n            name,\n            countries (\n              name\n            )\n          )\n        )\n      ");customerId&&(query=query.eq("customer_id",customerId));const{data:data,error:error}=await query.order("created_at",{ascending:!1});if(error)throw error;return{data:data,error:null}}catch(error){return console.error("Error fetching bookings:",error),{data:null,error:error}}};export const createBooking=async bookingData=>{try{const bookingReference=`BT${Date.now()}${Math.random().toString(36).substr(2,4).toUpperCase()}`,{data:data,error:error}=await supabase.from("bookings").insert([{...bookingData,booking_reference:bookingReference}]).select().single();if(error)throw error;return{data:data,error:null}}catch(error){return console.error("Error creating booking:",error),{data:null,error:error}}};export const updateBookingStatus=async(id,status)=>{try{const{data:data,error:error}=await supabase.from("bookings").update({status:status}).eq("id",id).select().single();if(error)throw error;return{data:data,error:null}}catch(error){return console.error("Error updating booking status:",error),{data:null,error:error}}};export const getSettings=async()=>{try{const{data:data,error:error}=await supabase.from("website_settings").select("*");if(error)throw error;const settings={};return data.forEach((setting=>{settings[setting.key]=setting.value})),{data:settings,error:null}}catch(error){return console.error("Error fetching settings:",error),{data:null,error:error}}};export const updateSetting=async(key,value)=>{try{const{data:data,error:error}=await supabase.from("website_settings").upsert({key:key,value:value}).select().single();if(error)throw error;return{data:data,error:null}}catch(error){return console.error("Error updating setting:",error),{data:null,error:error}}};export const getEmailCampaigns=async()=>{try{const{data:data,error:error}=await supabase.from("email_campaigns").select("*").order("created_at",{ascending:!1});if(error)throw error;return{data:data,error:null}}catch(error){return console.error("Error fetching email campaigns:",error),{data:null,error:error}}};export const createEmailCampaign=async campaignData=>{try{const{data:data,error:error}=await supabase.from("email_campaigns").insert([campaignData]).select().single();if(error)throw error;return{data:data,error:null}}catch(error){return console.error("Error creating email campaign:",error),{data:null,error:error}}};export const updateEmailCampaign=async(id,campaignData)=>{try{const{data:data,error:error}=await supabase.from("email_campaigns").update(campaignData).eq("id",id).select().single();if(error)throw error;return{data:data,error:null}}catch(error){return console.error("Error updating email campaign:",error),{data:null,error:error}}};export const deleteEmailCampaign=async id=>{try{const{error:error}=await supabase.from("email_campaigns").delete().eq("id",id);if(error)throw error;return{error:null}}catch(error){return console.error("Error deleting email campaign:",error),{error:error}}};export const getNewsletterSubscribers=async(activeOnly=!0)=>{try{let query=supabase.from("newsletter_subscribers").select("*");activeOnly&&(query=query.eq("is_active",!0));const{data:data,error:error}=await query.order("subscribed_at",{ascending:!1});if(error)throw error;return{data:data,error:null}}catch(error){return console.error("Error fetching newsletter subscribers:",error),{data:null,error:error}}};export const createNewsletterSubscriber=async subscriberData=>{try{const{data:data,error:error}=await supabase.from("newsletter_subscribers").insert([subscriberData]).select().single();if(error)throw error;return{data:data,error:null}}catch(error){return console.error("Error creating newsletter subscriber:",error),{data:null,error:error}}};export const updateNewsletterSubscriber=async(id,subscriberData)=>{try{const{data:data,error:error}=await supabase.from("newsletter_subscribers").update(subscriberData).eq("id",id).select().single();if(error)throw error;return{data:data,error:null}}catch(error){return console.error("Error updating newsletter subscriber:",error),{data:null,error:error}}};export const unsubscribeNewsletter=async email=>{try{const{data:data,error:error}=await supabase.from("newsletter_subscribers").update({is_active:!1,unsubscribed_at:(new Date).toISOString()}).eq("email",email).select().single();if(error)throw error;return{data:data,error:null}}catch(error){return console.error("Error unsubscribing from newsletter:",error),{data:null,error:error}}};export const getDashboardStats=async()=>{try{const[{count:totalBookings},{count:totalCustomers},{count:totalHotels},{count:totalCountries},{count:pendingBookings},{count:confirmedBookings},{count:newsletterSubscribers}]=await Promise.all([supabase.from("bookings").select("*",{count:"exact",head:!0}),supabase.from("customers").select("*",{count:"exact",head:!0}),supabase.from("hotels").select("*",{count:"exact",head:!0}).eq("is_active",!0),supabase.from("countries").select("*",{count:"exact",head:!0}),supabase.from("bookings").select("*",{count:"exact",head:!0}).eq("status","pending"),supabase.from("bookings").select("*",{count:"exact",head:!0}).eq("status","confirmed"),supabase.from("newsletter_subscribers").select("*",{count:"exact",head:!0}).eq("is_active",!0)]),{data:recentBookings,error:bookingsError}=await supabase.from("bookings").select("\n        *,\n        customers (first_name, last_name, email),\n        hotels (name, cities (name, countries (name)))\n      ").order("created_at",{ascending:!1}).limit(5);if(bookingsError)throw bookingsError;const thirtyDaysAgo=new Date;thirtyDaysAgo.setDate(thirtyDaysAgo.getDate()-30);const{data:revenueData,error:revenueError}=await supabase.from("bookings").select("total_amount, currency, created_at").gte("created_at",thirtyDaysAgo.toISOString()).eq("status","confirmed");if(revenueError)throw revenueError;return{data:{totalBookings:totalBookings||0,totalCustomers:totalCustomers||0,totalHotels:totalHotels||0,totalCountries:totalCountries||0,pendingBookings:pendingBookings||0,confirmedBookings:confirmedBookings||0,newsletterSubscribers:newsletterSubscribers||0,totalRevenue:revenueData?.reduce(((sum,booking)=>sum+(booking.total_amount||0)),0)||0,recentBookings:recentBookings||[],revenueData:revenueData||[]},error:null}}catch(error){return console.error("Error fetching dashboard stats:",error),{data:null,error:error}}};