import { NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/auth';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const countryId = searchParams.get('country_id');
    const featuredOnly = searchParams.get('featured') === 'true';

    if (!countryId) {
      return NextResponse.json(
        { error: 'Country ID is required' },
        { status: 400 }
      );
    }

    const supabase = await createServerSupabaseClient();
    
    let query = supabase
      .from('cities')
      .select(`
        id,
        name,
        region,
        description,
        image_url,
        highlights,
        best_time,
        duration,
        is_featured,
        countries (
          id,
          name,
          code,
          currency,
          is_featured
        ),
        hotels!inner (
          id
        )
      `)
      .eq('country_id', countryId)
      .eq('hotels.is_active', true);

    if (featuredOnly) {
      query = query.eq('is_featured', true);
    }

    query = query.order('name');

    const { data: cities, error } = await query;

    if (error) {
      console.error('Error fetching cities:', error);
      return NextResponse.json(
        { error: 'Failed to fetch cities' },
        { status: 500 }
      );
    }

    // Filter cities that have active hotels
    const citiesWithHotels = cities.filter(city => 
      city.hotels && city.hotels.length > 0
    );

    return NextResponse.json({
      success: true,
      data: citiesWithHotels
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
