"use client";
import { useState, useEffect } from "react";
import Image from "next/image";
import { Card, CardContent } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import {
  Search,
  Plus,
  MapPin,
  Star,
  Upload,
  Edit,
  Trash2,
  Globe,
  Flag,
} from "lucide-react";
import {
  getCountries,
  getCities,
  getHotels,
  createCountry,
  createCity,
  createHotel,
  updateCountry,
  updateCity,
  updateHotel,
  deleteHotel,
} from "@/lib/admin-api";
import {
  CountryModal,
  CityModal,
  HotelModal,
} from "@/components/admin/DestinationModals";

export default function DestinationsManagement() {
  // State management
  const [countries, setCountries] = useState([]);
  const [cities, setCities] = useState([]);
  const [hotels, setHotels] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("countries"); // countries, cities, hotels

  // Modal states
  const [isAddingCountry, setIsAddingCountry] = useState(false);
  const [isAddingCity, setIsAddingCity] = useState(false);
  const [isAddingHotel, setIsAddingHotel] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [editingType, setEditingType] = useState(null);

  // Form states
  const [countryForm, setCountryForm] = useState({
    name: "",
    code: "",
    currency: "",
    is_featured: false,
  });

  const [cityForm, setCityForm] = useState({
    name: "",
    country_id: "",
    region: "",
    description: "",
    image_url: "",
    highlights: [],
    best_time: "",
    duration: "",
    is_featured: false,
  });

  const [hotelForm, setHotelForm] = useState({
    name: "",
    city_id: "",
    stars: 3,
    description: "",
    price: "",
    currency: "DA",
    image_url: "",
    image_url_1: "",
    image_url_2: "",
    image_url_3: "",
    image_url_4: "",
    discount_percentage: 0,
    offer_text: "",
    highlights: [],
    amenities: [],
    is_featured: false,
    is_active: true,
  });

  // Data fetching
  useEffect(() => {
    fetchAllData();
  }, []);

  const fetchAllData = async () => {
    setLoading(true);
    try {
      const [countriesRes, citiesRes, hotelsRes] = await Promise.all([
        getCountries(),
        getCities(),
        getHotels(),
      ]);

      if (countriesRes.data) setCountries(countriesRes.data);
      if (citiesRes.data) setCities(citiesRes.data);
      if (hotelsRes.data) setHotels(hotelsRes.data);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
    }
  };

  // Form handlers
  const resetForms = () => {
    setCountryForm({ name: "", code: "", currency: "", is_featured: false });
    setCityForm({
      name: "",
      country_id: "",
      region: "",
      description: "",
      image_url: "",
      highlights: [],
      best_time: "",
      duration: "",
      is_featured: false,
    });
    setHotelForm({
      name: "",
      city_id: "",
      stars: 3,
      description: "",
      price: "",
      currency: "DA",
      image_url: "",
      image_url_1: "",
      image_url_2: "",
      image_url_3: "",
      image_url_4: "",
      discount_percentage: 0,
      offer_text: "",
      highlights: [],
      amenities: [],
      is_featured: false,
      is_active: true,
    });
    setEditingItem(null);
    setEditingType(null);
  };

  const handleSubmitCountry = async (e) => {
    e.preventDefault();
    try {
      if (editingType === "country" && editingItem) {
        const { data, error } = await updateCountry(
          editingItem.id,
          countryForm
        );
        if (error) throw error;
        setCountries(
          countries.map((c) => (c.id === editingItem.id ? data : c))
        );
      } else {
        const { data, error } = await createCountry(countryForm);
        if (error) throw error;
        setCountries([...countries, data]);
      }
      setIsAddingCountry(false);
      resetForms();
    } catch (error) {
      console.error("Error saving country:", error);
      alert("Erreur lors de la sauvegarde du pays");
    }
  };

  const handleSubmitCity = async (e) => {
    e.preventDefault();
    try {
      const cityData = {
        ...cityForm,
        highlights: cityForm.highlights.filter((h) => h.trim() !== ""),
      };

      if (editingType === "city" && editingItem) {
        const { data, error } = await updateCity(editingItem.id, cityData);
        if (error) throw error;
        setCities(cities.map((c) => (c.id === editingItem.id ? data : c)));
      } else {
        const { data, error } = await createCity(cityData);
        if (error) throw error;
        setCities([...cities, data]);
      }
      setIsAddingCity(false);
      resetForms();
    } catch (error) {
      console.error("Error saving city:", error);
      alert("Erreur lors de la sauvegarde de la ville");
    }
  };

  const handleSubmitHotel = async (e) => {
    e.preventDefault();
    try {
      const hotelData = {
        ...hotelForm,
        price: parseFloat(hotelForm.price),
        highlights: hotelForm.highlights.filter((h) => h.trim() !== ""),
        amenities: hotelForm.amenities.filter((a) => a.trim() !== ""),
      };

      if (editingType === "hotel" && editingItem) {
        const { data, error } = await updateHotel(editingItem.id, hotelData);
        if (error) throw error;
        setHotels(hotels.map((h) => (h.id === editingItem.id ? data : h)));
      } else {
        const { data, error } = await createHotel(hotelData);
        if (error) throw error;
        setHotels([...hotels, data]);
      }
      setIsAddingHotel(false);
      resetForms();
    } catch (error) {
      console.error("Error saving hotel:", error);
      alert("Erreur lors de la sauvegarde de l'hôtel");
    }
  };

  const handleDeleteHotel = async (hotelId) => {
    if (!confirm("Êtes-vous sûr de vouloir supprimer cet hôtel ?")) return;

    try {
      const { error } = await deleteHotel(hotelId);
      if (error) throw error;
      setHotels(hotels.filter((h) => h.id !== hotelId));
    } catch (error) {
      console.error("Error deleting hotel:", error);
      alert("Erreur lors de la suppression de l'hôtel");
    }
  };

  // Edit handlers
  const handleEditCountry = (country) => {
    setCountryForm({
      name: country.name,
      code: country.code,
      currency: country.currency,
      is_featured: country.is_featured,
    });
    setEditingItem(country);
    setEditingType("country");
    setIsAddingCountry(true);
  };

  const handleEditCity = (city) => {
    setCityForm({
      name: city.name,
      country_id: city.country_id,
      region: city.region || "",
      description: city.description || "",
      image_url: city.image_url || "",
      highlights: city.highlights || [],
      best_time: city.best_time || "",
      duration: city.duration || "",
      is_featured: city.is_featured,
    });
    setEditingItem(city);
    setEditingType("city");
    setIsAddingCity(true);
  };

  const handleEditHotel = (hotel) => {
    setHotelForm({
      name: hotel.name,
      city_id: hotel.city_id,
      stars: hotel.stars,
      description: hotel.description || "",
      price: hotel.price.toString(),
      currency: hotel.currency,
      image_url: hotel.image_url || "",
      image_url_1: hotel.image_url_1 || "",
      image_url_2: hotel.image_url_2 || "",
      image_url_3: hotel.image_url_3 || "",
      image_url_4: hotel.image_url_4 || "",
      discount_percentage: hotel.discount_percentage || 0,
      offer_text: hotel.offer_text || "",
      highlights: hotel.highlights || [],
      amenities: hotel.amenities || [],
      is_featured: hotel.is_featured,
      is_active: hotel.is_active,
    });
    setEditingItem(hotel);
    setEditingType("hotel");
    setIsAddingHotel(true);
  };

  // Array field handlers
  const addHighlight = (type) => {
    if (type === "city") {
      setCityForm({ ...cityForm, highlights: [...cityForm.highlights, ""] });
    } else if (type === "hotel") {
      setHotelForm({ ...hotelForm, highlights: [...hotelForm.highlights, ""] });
    }
  };

  const updateHighlight = (type, index, value) => {
    if (type === "city") {
      const newHighlights = [...cityForm.highlights];
      newHighlights[index] = value;
      setCityForm({ ...cityForm, highlights: newHighlights });
    } else if (type === "hotel") {
      const newHighlights = [...hotelForm.highlights];
      newHighlights[index] = value;
      setHotelForm({ ...hotelForm, highlights: newHighlights });
    }
  };

  const removeHighlight = (type, index) => {
    if (type === "city") {
      setCityForm({
        ...cityForm,
        highlights: cityForm.highlights.filter((_, i) => i !== index),
      });
    } else if (type === "hotel") {
      setHotelForm({
        ...hotelForm,
        highlights: hotelForm.highlights.filter((_, i) => i !== index),
      });
    }
  };

  const addAmenity = () => {
    setHotelForm({ ...hotelForm, amenities: [...hotelForm.amenities, ""] });
  };

  const updateAmenity = (index, value) => {
    const newAmenities = [...hotelForm.amenities];
    newAmenities[index] = value;
    setHotelForm({ ...hotelForm, amenities: newAmenities });
  };

  const removeAmenity = (index) => {
    setHotelForm({
      ...hotelForm,
      amenities: hotelForm.amenities.filter((_, i) => i !== index),
    });
  };

  // Filter data based on search
  const filteredCountries = countries.filter((country) =>
    country.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredCities = cities.filter(
    (city) =>
      city.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      city.countries?.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredHotels = hotels.filter(
    (hotel) =>
      hotel.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      hotel.cities?.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-lg text-gray-600">Chargement...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container-custom">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Gestion des Destinations
          </h1>
          <div className="flex gap-2">
            {activeTab === "countries" && (
              <Button
                variant="primary"
                icon={<Plus className="w-4 h-4" />}
                onClick={() => setIsAddingCountry(true)}
              >
                Ajouter un Pays
              </Button>
            )}
            {activeTab === "cities" && (
              <Button
                variant="primary"
                icon={<Plus className="w-4 h-4" />}
                onClick={() => setIsAddingCity(true)}
              >
                Ajouter une Ville
              </Button>
            )}
            {activeTab === "hotels" && (
              <Button
                variant="primary"
                icon={<Plus className="w-4 h-4" />}
                onClick={() => setIsAddingHotel(true)}
              >
                Ajouter un Hôtel
              </Button>
            )}
          </div>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg">
          <button
            onClick={() => setActiveTab("countries")}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === "countries"
                ? "bg-white text-primary shadow-sm"
                : "text-gray-600 hover:text-gray-900"
            }`}
          >
            <Globe className="w-4 h-4 inline mr-2" />
            Pays ({countries.length})
          </button>
          <button
            onClick={() => setActiveTab("cities")}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === "cities"
                ? "bg-white text-primary shadow-sm"
                : "text-gray-600 hover:text-gray-900"
            }`}
          >
            <MapPin className="w-4 h-4 inline mr-2" />
            Villes ({cities.length})
          </button>
          <button
            onClick={() => setActiveTab("hotels")}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === "hotels"
                ? "bg-white text-primary shadow-sm"
                : "text-gray-600 hover:text-gray-900"
            }`}
          >
            <Star className="w-4 h-4 inline mr-2" />
            Hôtels ({hotels.length})
          </button>
        </div>

        {/* Search */}
        <div className="mb-6">
          <Input
            type="text"
            placeholder={`Rechercher ${
              activeTab === "countries"
                ? "des pays"
                : activeTab === "cities"
                ? "des villes"
                : "des hôtels"
            }...`}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            icon={<Search className="w-4 h-4" />}
          />
        </div>

        {/* Content based on active tab */}
        {activeTab === "countries" && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredCountries.map((country) => (
              <Card key={country.id} className="overflow-hidden">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <Flag className="w-6 h-6 text-primary" />
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          {country.name}
                        </h3>
                        <p className="text-sm text-gray-500">
                          {country.code} • {country.currency}
                        </p>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditCountry(country)}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  {country.is_featured && (
                    <div className="mb-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-secondary text-white">
                        Pays vedette
                      </span>
                    </div>
                  )}
                  <div className="text-sm text-gray-600">
                    <p>
                      {cities.filter((c) => c.country_id === country.id).length}{" "}
                      ville(s)
                    </p>
                    <p>
                      {
                        hotels.filter((h) =>
                          cities.find(
                            (c) =>
                              c.id === h.city_id && c.country_id === country.id
                          )
                        ).length
                      }{" "}
                      hôtel(s)
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {activeTab === "cities" && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredCities.map((city) => (
              <Card key={city.id} className="overflow-hidden">
                <div className="relative h-48">
                  <Image
                    src={city.image_url || "/images/back.png"}
                    alt={city.name}
                    width={400}
                    height={192}
                    className="w-full h-full object-cover"
                  />
                </div>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        {city.name}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {city.region && `${city.region}, `}
                        {city.countries?.name}
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditCity(city)}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  {city.is_featured && (
                    <div className="mb-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-secondary text-white">
                        Ville vedette
                      </span>
                    </div>
                  )}
                  <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                    {city.description}
                  </p>
                  <div className="text-sm text-gray-600">
                    <p>
                      {hotels.filter((h) => h.city_id === city.id).length}{" "}
                      hôtel(s)
                    </p>
                    {city.duration && <p>Durée: {city.duration}</p>}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {activeTab === "hotels" && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredHotels.map((hotel) => (
              <Card key={hotel.id} className="overflow-hidden">
                <div className="relative h-48">
                  <img
                    src={hotel.image_url || "/images/back.png"}
                    alt={hotel.name}
                    className="w-full h-full object-cover"
                  />
                  {hotel.discount_percentage > 0 && (
                    <div className="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-sm font-bold">
                      -{hotel.discount_percentage}%
                    </div>
                  )}
                </div>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        {hotel.name}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {hotel.cities?.name}, {hotel.cities?.countries?.name}
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditHotel(hotel)}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteHotel(hotel.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 mb-2">
                    <div className="flex items-center text-accent">
                      {[...Array(hotel.stars)].map((_, i) => (
                        <Star key={i} className="w-4 h-4 fill-current" />
                      ))}
                    </div>
                    {hotel.is_featured && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-secondary text-white">
                        Vedette
                      </span>
                    )}
                  </div>
                  <p className="text-lg font-bold text-primary mb-2">
                    {hotel.price.toLocaleString()} {hotel.currency}
                  </p>
                  {hotel.offer_text && (
                    <p className="text-sm text-green-600 mb-2">
                      {hotel.offer_text}
                    </p>
                  )}
                  <div className="flex items-center justify-between text-sm">
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${
                        hotel.is_active
                          ? "bg-green-100 text-green-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {hotel.is_active ? "Actif" : "Inactif"}
                    </span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Modals */}
        <CountryModal
          isOpen={isAddingCountry}
          onClose={() => {
            setIsAddingCountry(false);
            resetForms();
          }}
          onSubmit={handleSubmitCountry}
          form={countryForm}
          setForm={setCountryForm}
          isEditing={editingType === "country"}
        />

        <CityModal
          isOpen={isAddingCity}
          onClose={() => {
            setIsAddingCity(false);
            resetForms();
          }}
          onSubmit={handleSubmitCity}
          form={cityForm}
          setForm={setCityForm}
          countries={countries}
          isEditing={editingType === "city"}
          onAddHighlight={addHighlight}
          onUpdateHighlight={updateHighlight}
          onRemoveHighlight={removeHighlight}
        />

        <HotelModal
          isOpen={isAddingHotel}
          onClose={() => {
            setIsAddingHotel(false);
            resetForms();
          }}
          onSubmit={handleSubmitHotel}
          form={hotelForm}
          setForm={setHotelForm}
          cities={cities}
          isEditing={editingType === "hotel"}
          onAddHighlight={addHighlight}
          onUpdateHighlight={updateHighlight}
          onRemoveHighlight={removeHighlight}
          onAddAmenity={addAmenity}
          onUpdateAmenity={updateAmenity}
          onRemoveAmenity={removeAmenity}
        />
      </div>
    </div>
  );
}
