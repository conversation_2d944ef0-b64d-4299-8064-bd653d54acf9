"use client";
import { useState, useEffect } from 'react';
import { testCloudinaryConnection } from '@/lib/cloudinary-client';
import Button from '@/components/ui/Button';
import { CloudinaryImageUpload } from './CloudinaryImageUpload';
import { CheckCircle, XCircle, AlertCircle, Upload, Settings, Loader2 } from 'lucide-react';

export const CloudinaryTest = () => {
  const [testResult, setTestResult] = useState(null);
  const [testing, setTesting] = useState(false);
  const [uploadedImages, setUploadedImages] = useState([]);
  const [config, setConfig] = useState(null);
  const [loadingConfig, setLoadingConfig] = useState(true);

  // Load configuration from server on component mount
  useEffect(() => {
    const loadConfig = async () => {
      try {
        const response = await fetch('/api/cloudinary/config');
        const result = await response.json();

        if (result.success) {
          setConfig(result.config);
        } else {
          throw new Error(result.error || 'Failed to get configuration');
        }
      } catch (error) {
        console.error('Failed to load config:', error);
        // Fallback to basic client-side check
        const cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME;
        setConfig({
          cloudName: cloudName,
          hasApiKey: false, // Can't check from client
          hasApiSecret: false, // Can't check from client
          hasCloudinaryUrl: false, // Can't check from client
          isConfigured: !!cloudName // Only check what we can verify
        });
      } finally {
        setLoadingConfig(false);
      }
    };

    loadConfig();
  }, []);

  const handleTestConnection = async () => {
    setTesting(true);
    try {
      const result = await testCloudinaryConnection();
      setTestResult(result);
    } catch (error) {
      setTestResult({
        success: false,
        error: error.message
      });
    } finally {
      setTesting(false);
    }
  };

  const handleImageUploaded = (imageData) => {
    setUploadedImages(prev => [...prev, imageData]);
  };

  const getStatusIcon = (status) => {
    if (status === true) return <CheckCircle className="w-5 h-5 text-green-500" />;
    if (status === false) return <XCircle className="w-5 h-5 text-red-500" />;
    return <AlertCircle className="w-5 h-5 text-yellow-500" />;
  };

  const getStatusText = (status) => {
    if (status === true) return 'Configuré';
    if (status === false) return 'Non configuré';
    return 'Partiellement configuré';
  };

  // Show loading state while config is being fetched
  if (loadingConfig) {
    return (
      <div className="max-w-4xl mx-auto p-6 space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Test d'intégration Cloudinary
          </h1>
          <p className="text-gray-600">
            Vérifiez que votre configuration Cloudinary fonctionne correctement
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-center space-x-2">
            <Loader2 className="w-5 h-5 animate-spin text-blue-500" />
            <span className="text-gray-600">Chargement de la configuration...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Test d'intégration Cloudinary
        </h1>
        <p className="text-gray-600">
          Vérifiez que votre configuration Cloudinary fonctionne correctement
        </p>
      </div>

      {/* Configuration Status */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center gap-2 mb-4">
          <Settings className="w-5 h-5 text-gray-600" />
          <h2 className="text-xl font-semibold text-gray-900">
            État de la configuration
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium text-gray-700">Cloud Name</span>
              <div className="flex items-center gap-2">
                {getStatusIcon(!!config?.cloudName)}
                <span className="text-sm text-gray-600">
                  {config?.cloudName || 'Non défini'}
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium text-gray-700">API Key</span>
              <div className="flex items-center gap-2">
                {getStatusIcon(config?.hasApiKey)}
                <span className="text-sm text-gray-600">
                  {getStatusText(config?.hasApiKey)}
                </span>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium text-gray-700">API Secret</span>
              <div className="flex items-center gap-2">
                {getStatusIcon(config?.hasApiSecret)}
                <span className="text-sm text-gray-600">
                  {getStatusText(config?.hasApiSecret)}
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium text-gray-700">Configuration complète</span>
              <div className="flex items-center gap-2">
                {getStatusIcon(config?.isConfigured)}
                <span className="text-sm text-gray-600">
                  {getStatusText(config?.isConfigured)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Connection Test */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">
            Test de connexion
          </h2>
          <Button
            onClick={handleTestConnection}
            disabled={testing || !config?.isConfigured}
            variant="primary"
          >
            {testing ? 'Test en cours...' : 'Tester la connexion'}
          </Button>
        </div>

        {testResult && (
          <div className={`p-4 rounded-lg ${
            testResult.success 
              ? 'bg-green-50 border border-green-200' 
              : 'bg-red-50 border border-red-200'
          }`}>
            <div className="flex items-center gap-2 mb-2">
              {testResult.success 
                ? <CheckCircle className="w-5 h-5 text-green-500" />
                : <XCircle className="w-5 h-5 text-red-500" />
              }
              <span className={`font-medium ${
                testResult.success ? 'text-green-800' : 'text-red-800'
              }`}>
                {testResult.success ? 'Connexion réussie!' : 'Échec de la connexion'}
              </span>
            </div>
            <p className={`text-sm ${
              testResult.success ? 'text-green-700' : 'text-red-700'
            }`}>
              {testResult.message || testResult.error}
            </p>
            {testResult.cloudName && (
              <p className="text-sm text-gray-600 mt-1">
                Cloud: {testResult.cloudName}
              </p>
            )}
          </div>
        )}
      </div>

      {/* Upload Test */}
      {config?.isConfigured && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center gap-2 mb-4">
            <Upload className="w-5 h-5 text-gray-600" />
            <h2 className="text-xl font-semibold text-gray-900">
              Test d'upload d'images
            </h2>
          </div>

          <CloudinaryImageUpload
            onImageUploaded={handleImageUploaded}
            folder="test"
            multiple={true}
            maxFiles={5}
          />

          {uploadedImages.length > 0 && (
            <div className="mt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-3">
                Images uploadées ({uploadedImages.length})
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {uploadedImages.map((image, index) => (
                  <div key={index} className="bg-gray-50 rounded-lg p-4">
                    <img
                      src={image.url}
                      alt={`Upload ${index + 1}`}
                      className="w-full h-32 object-cover rounded mb-2"
                    />
                    <div className="text-xs text-gray-600 space-y-1">
                      <p><strong>Public ID:</strong> {image.public_id}</p>
                      <p><strong>Format:</strong> {image.format}</p>
                      <p><strong>Taille:</strong> {Math.round(image.bytes / 1024)} KB</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {!config?.isConfigured && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <div className="flex items-center gap-2 mb-2">
            <AlertCircle className="w-5 h-5 text-yellow-600" />
            <h3 className="text-lg font-medium text-yellow-800">
              Configuration incomplète
            </h3>
          </div>
          <p className="text-yellow-700 mb-4">
            Veuillez vérifier que toutes les variables d'environnement Cloudinary sont correctement configurées dans votre fichier .env :
          </p>
          <ul className="list-disc list-inside text-yellow-700 space-y-1 text-sm">
            <li>NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME</li>
            <li>CLOUDINARY_API_KEY</li>
            <li>CLOUDINARY_API_SECRET</li>
          </ul>
        </div>
      )}
    </div>
  );
};

export default CloudinaryTest;
