"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { createBrowserClient } from "@supabase/ssr";
import { useRouter } from "next/navigation";

const SessionContext = createContext({});

export const useSession = () => {
  const context = useContext(SessionContext);
  if (!context) {
    throw new Error("useSession must be used within a SessionProvider");
  }
  return context;
};

export function SessionProvider({ children, initialSession = null }) {
  const [session, setSession] = useState(initialSession);
  const [user, setUser] = useState(initialSession?.user || null);
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(!initialSession);
  const router = useRouter();

  // Create Supabase client
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  );

  // Fetch user profile
  const fetchProfile = async (userId) => {
    try {
      const { data, error } = await supabase
        .from("profiles")
        .select("*")
        .eq("user_id", userId)
        .single();

      if (error) {
        console.error("Error fetching profile:", error);
        return null;
      }

      return data;
    } catch (error) {
      console.error("Error in fetchProfile:", error);
      return null;
    }
  };

  // Initialize session
  useEffect(() => {
    const initializeSession = async () => {
      try {
        // Get initial session
        const {
          data: { session: currentSession },
          error,
        } = await supabase.auth.getSession();

        if (error) {
          console.error("Error getting session:", error);
          setSession(null);
          setUser(null);
          setProfile(null);
        } else if (currentSession) {
          setSession(currentSession);
          setUser(currentSession.user);

          // Fetch user profile
          const userProfile = await fetchProfile(currentSession.user.id);
          setProfile(userProfile);
        } else {
          setSession(null);
          setUser(null);
          setProfile(null);
        }
      } catch (error) {
        console.error("Error initializing session:", error);
        setSession(null);
        setUser(null);
        setProfile(null);
      } finally {
        setLoading(false);
      }
    };

    if (!initialSession) {
      initializeSession();
    } else {
      // If we have an initial session, fetch the profile
      if (initialSession.user) {
        fetchProfile(initialSession.user.id).then(setProfile);
      }
      setLoading(false);
    }
  }, [supabase.auth, initialSession]);

  // Listen for auth changes
  useEffect(() => {
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, currentSession) => {
      console.log("Auth state changed:", event, currentSession?.user?.email);

      if (event === "SIGNED_IN" && currentSession) {
        setSession(currentSession);
        setUser(currentSession.user);

        // Fetch user profile
        const userProfile = await fetchProfile(currentSession.user.id);
        setProfile(userProfile);

        // Refresh the page to update middleware state
        router.refresh();
      } else if (event === "SIGNED_OUT") {
        setSession(null);
        setUser(null);
        setProfile(null);

        // Redirect to signin if on admin page
        if (window.location.pathname.startsWith("/admin")) {
          router.push("/signin");
        }
      } else if (event === "TOKEN_REFRESHED" && currentSession) {
        console.log("Token refreshed successfully");
        setSession(currentSession);
        setUser(currentSession.user);
      }
    });

    return () => subscription.unsubscribe();
  }, [supabase.auth, router]);

  // Refresh session function
  const refreshSession = async () => {
    try {
      const {
        data: { session: refreshedSession },
        error,
      } = await supabase.auth.refreshSession();

      if (error) {
        console.error("Error refreshing session:", error);
        return false;
      }

      if (refreshedSession) {
        setSession(refreshedSession);
        setUser(refreshedSession.user);
        console.log("Session refreshed successfully");
        return true;
      }

      return false;
    } catch (error) {
      console.error("Error in refreshSession:", error);
      return false;
    }
  };

  // Automatic token refresh for admin pages
  useEffect(() => {
    let refreshInterval;

    if (session && window.location.pathname.startsWith("/admin")) {
      // Check token expiration every 5 minutes
      refreshInterval = setInterval(async () => {
        if (session.expires_at) {
          const expiresAt = new Date(session.expires_at * 1000);
          const now = new Date();
          const timeUntilExpiry = expiresAt.getTime() - now.getTime();
          const fifteenMinutes = 15 * 60 * 1000; // 15 minutes in milliseconds

          // Refresh token if it expires within 15 minutes
          if (timeUntilExpiry < fifteenMinutes && timeUntilExpiry > 0) {
            console.log(
              "Auto-refreshing token (expires in",
              Math.round(timeUntilExpiry / 60000),
              "minutes)"
            );
            const success = await refreshSession();
            if (!success) {
              console.error("Failed to refresh token, redirecting to signin");
              router.push(
                "/signin?error=session_expired&message=Session expired, please sign in again"
              );
            }
          }
        }
      }, 5 * 60 * 1000); // Check every 5 minutes
    }

    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [session, router, supabase.auth]);

  // Sign out function
  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error("Error signing out:", error);
        // Don't throw error for sign out - just clear local state
      }

      // Clear local state regardless of API response
      setSession(null);
      setUser(null);
      setProfile(null);

      // Redirect to home page
      router.push("/");
      router.refresh();
    } catch (error) {
      console.error("Error in signOut:", error);
      // Clear local state even if there's an error
      setSession(null);
      setUser(null);
      setProfile(null);
      router.push("/");
    }
  };

  // Check if user is admin
  const isAdmin = () => {
    return profile?.role === "admin";
  };

  const value = {
    session,
    user,
    profile,
    loading,
    isAdmin,
    signOut,
    refreshSession,
    supabase,
  };

  return (
    <SessionContext.Provider value={value}>{children}</SessionContext.Provider>
  );
}
