"use client";
import { useEffect, useState } from "react";
import BookingSearchBox from "./BookingSearchBox";
import WhatsAppBubble from "../WhatsAppBubble/WhatsAppBubble";
import TopBookNow from "@/components/landingpage/TopBookNow";
import { getTopHotels } from "@/lib/database";
import Hero from "../hero/Hero";
import Partners from "../hero/Partners";
import TopHotelsSection from "@/app/top_book_now/page";

export default function LandingPage() {
  const [topHotel, setTopHotel] = useState(null);
  const images = [
    "/images/ExploreTunisia/exploretunisia1.jpg",
    "/images/ExploreTunisia/exploretunisia2.jpg",
    "/images/ExploreTunisia/exploretunisia3.jpg",
    "/images/ExploreTunisia/explorebackground.jpg",
  ]; // Replace with your actual image paths
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  useEffect(() => {
    async function fetchTopHotel() {
      const hotels = await getTopHotels();
      if (hotels && hotels.length > 0) {
        setTopHotel(hotels[0]);
      }
    }

    fetchTopHotel();
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) => (prevIndex + 1) % images.length);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return (
    <>
      <section className="relative h-[60vh] md:h-[80vh] min-h-[400px] w-full overflow-hidden flex items-end justify-center">
        <img
          src={images[currentImageIndex]}
          alt="Travel background"
          className="absolute inset-0 w-full h-full object-cover z-0 transition-opacity duration-1000"
        />
      </section>

      <div className="relative z-30 -mt-16 flex justify-center">
        <BookingSearchBox />
      </div>

      <div className="h-24 md:h-28 lg:h-32" />

      <Hero />

      <h2 className="text-heading text-3xl md:text-4xl text-center mb-10 tracking-tight text-gray-900">
        NOS PARTENAIRES
      </h2>

      <Partners />
      <Partners />

      <TopHotelsSection />

      <WhatsAppBubble />
    </>
  );
}
