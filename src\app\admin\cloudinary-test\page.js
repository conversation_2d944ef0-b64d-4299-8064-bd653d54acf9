"use client";
import { Suspense } from 'react';
import CloudinaryTest from '@/components/admin/CloudinaryTest';

function LoadingSpinner() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Chargement du test Cloudinary...</p>
      </div>
    </div>
  );
}

export default function CloudinaryTestPage() {
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <div className="min-h-screen bg-gray-50 py-8">
        <CloudinaryTest />
      </div>
    </Suspense>
  );
}
