-- Hotel Images Migration Script
-- This script migrates from Cloudinary integration to direct URL storage
-- Run this in your Supabase SQL Editor

-- Step 1: Add new URL columns to hotels table for storing additional image URLs
ALTER TABLE hotels ADD COLUMN IF NOT EXISTS image_url_1 TEXT;
ALTER TABLE hotels ADD COLUMN IF NOT EXISTS image_url_2 TEXT;
ALTER TABLE hotels ADD COLUMN IF NOT EXISTS image_url_3 TEXT;
ALTER TABLE hotels ADD COLUMN IF NOT EXISTS image_url_4 TEXT;

-- Step 2: Add comments to document the new fields
COMMENT ON COLUMN hotels.image_url IS 'Primary hotel image URL (existing field)';
COMMENT ON COLUMN hotels.image_url_1 IS 'Additional hotel image URL #1';
COMMENT ON COLUMN hotels.image_url_2 IS 'Additional hotel image URL #2';
COMMENT ON COLUMN hotels.image_url_3 IS 'Additional hotel image URL #3';
COMMENT ON COLUMN hotels.image_url_4 IS 'Additional hotel image URL #4';

-- Step 3: Create indexes for the new URL columns (optional, for search functionality)
-- CREATE INDEX IF NOT EXISTS idx_hotels_image_url_1 ON hotels(image_url_1) WHERE image_url_1 IS NOT NULL;
-- CREATE INDEX IF NOT EXISTS idx_hotels_image_url_2 ON hotels(image_url_2) WHERE image_url_2 IS NOT NULL;
-- CREATE INDEX IF NOT EXISTS idx_hotels_image_url_3 ON hotels(image_url_3) WHERE image_url_3 IS NOT NULL;
-- CREATE INDEX IF NOT EXISTS idx_hotels_image_url_4 ON hotels(image_url_4) WHERE image_url_4 IS NOT NULL;

-- Step 4: Add validation constraints (optional)
-- These ensure URL fields are either NULL or valid URL format
-- ALTER TABLE hotels ADD CONSTRAINT check_image_url_1_format 
-- CHECK (
--   image_url_1 IS NULL OR 
--   image_url_1 ~ '^https?://[^\s/$.?#].[^\s]*$'
-- );

-- ALTER TABLE hotels ADD CONSTRAINT check_image_url_2_format 
-- CHECK (
--   image_url_2 IS NULL OR 
--   image_url_2 ~ '^https?://[^\s/$.?#].[^\s]*$'
-- );

-- ALTER TABLE hotels ADD CONSTRAINT check_image_url_3_format 
-- CHECK (
--   image_url_3 IS NULL OR 
--   image_url_3 ~ '^https?://[^\s/$.?#].[^\s]*$'
-- );

-- ALTER TABLE hotels ADD CONSTRAINT check_image_url_4_format 
-- CHECK (
--   image_url_4 IS NULL OR 
--   image_url_4 ~ '^https?://[^\s/$.?#].[^\s]*$'
-- );

-- Step 5: Create a function to get all hotel images as an array
CREATE OR REPLACE FUNCTION get_hotel_image_urls(hotel_row hotels)
RETURNS TEXT[] AS $$
BEGIN
  RETURN ARRAY[
    hotel_row.image_url,
    hotel_row.image_url_1,
    hotel_row.image_url_2,
    hotel_row.image_url_3,
    hotel_row.image_url_4
  ];
END;
$$ LANGUAGE plpgsql;

-- Step 6: Create a function to get non-null hotel images
CREATE OR REPLACE FUNCTION get_hotel_image_urls_filtered(hotel_row hotels)
RETURNS TEXT[] AS $$
DECLARE
  image_urls TEXT[] := ARRAY[]::TEXT[];
BEGIN
  IF hotel_row.image_url IS NOT NULL THEN
    image_urls := array_append(image_urls, hotel_row.image_url);
  END IF;
  
  IF hotel_row.image_url_1 IS NOT NULL THEN
    image_urls := array_append(image_urls, hotel_row.image_url_1);
  END IF;
  
  IF hotel_row.image_url_2 IS NOT NULL THEN
    image_urls := array_append(image_urls, hotel_row.image_url_2);
  END IF;
  
  IF hotel_row.image_url_3 IS NOT NULL THEN
    image_urls := array_append(image_urls, hotel_row.image_url_3);
  END IF;
  
  IF hotel_row.image_url_4 IS NOT NULL THEN
    image_urls := array_append(image_urls, hotel_row.image_url_4);
  END IF;
  
  RETURN image_urls;
END;
$$ LANGUAGE plpgsql;

-- Step 7: Verify the migration
-- SELECT 
--   id,
--   name,
--   image_url,
--   image_url_1,
--   image_url_2,
--   image_url_3,
--   image_url_4,
--   get_hotel_image_urls_filtered(hotels.*) as all_images
-- FROM hotels 
-- WHERE image_url IS NOT NULL
-- LIMIT 5;

-- Step 8: Sample data migration from Cloudinary (if needed)
-- This is an example of how you might migrate existing Cloudinary images to direct URLs
-- You would need to run this after manually extracting the URLs from your Cloudinary account

-- Example migration script (customize as needed):
-- UPDATE hotels 
-- SET 
--   image_url_1 = 'https://example.com/hotel1_image1.jpg',
--   image_url_2 = 'https://example.com/hotel1_image2.jpg',
--   image_url_3 = 'https://example.com/hotel1_image3.jpg',
--   image_url_4 = 'https://example.com/hotel1_image4.jpg'
-- WHERE id = 'your-hotel-uuid-here';
