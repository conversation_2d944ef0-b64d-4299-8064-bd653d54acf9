import { NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/auth';

export async function GET() {
  try {
    const supabase = await createServerSupabaseClient();

    // Get all countries with cities that have active hotels
    const { data: countries, error: countriesError } = await supabase
      .from('countries')
      .select(`
        id,
        name,
        cities (
          id,
          name,
          hotels!inner (
            id
          )
        )
      `)
      .eq('cities.hotels.is_active', true);

    if (countriesError) {
      console.error('Error fetching countries:', countriesError);
    }

    // Get price range from active hotels
    const { data: priceRange, error: priceError } = await supabase
      .from('hotels')
      .select('price')
      .eq('is_active', true)
      .not('price', 'is', null);

    if (priceError) {
      console.error('Error fetching price range:', priceError);
    }

    // Calculate min and max prices
    let minPrice = 0;
    let maxPrice = 1000;
    
    if (priceRange && priceRange.length > 0) {
      const prices = priceRange.map(h => h.price).filter(p => p !== null);
      if (prices.length > 0) {
        minPrice = Math.min(...prices);
        maxPrice = Math.max(...prices);
      }
    }

    // Get all unique amenities from active hotels
    const { data: hotelsWithAmenities, error: amenitiesError } = await supabase
      .from('hotels')
      .select('amenities')
      .eq('is_active', true)
      .not('amenities', 'is', null);

    if (amenitiesError) {
      console.error('Error fetching amenities:', amenitiesError);
    }

    // Extract unique amenities
    const allAmenities = new Set();
    if (hotelsWithAmenities) {
      hotelsWithAmenities.forEach(hotel => {
        if (hotel.amenities && Array.isArray(hotel.amenities)) {
          hotel.amenities.forEach(amenity => allAmenities.add(amenity));
        }
      });
    }

    // Get star ratings distribution
    const { data: starRatings, error: starsError } = await supabase
      .from('hotels')
      .select('stars')
      .eq('is_active', true)
      .not('stars', 'is', null);

    if (starsError) {
      console.error('Error fetching star ratings:', starsError);
    }

    // Count hotels by star rating
    const starCounts = {};
    if (starRatings) {
      starRatings.forEach(hotel => {
        const stars = hotel.stars;
        starCounts[stars] = (starCounts[stars] || 0) + 1;
      });
    }

    // Format countries and cities data
    const formattedCountries = (countries || []).map(country => ({
      id: country.id,
      name: country.name,
      cities: (country.cities || [])
        .filter(city => city.hotels && city.hotels.length > 0)
        .map(city => ({
          id: city.id,
          name: city.name,
          hotelCount: city.hotels.length
        }))
    })).filter(country => country.cities.length > 0);

    // Common amenities list (you can customize this based on your data)
    const commonAmenities = [
      'WiFi',
      'Parking',
      'Pool',
      'Gym',
      'Spa',
      'Restaurant',
      'Bar',
      'Room Service',
      'Air Conditioning',
      'TV',
      'Mini Bar',
      'Safe',
      'Balcony',
      'Sea View',
      'Mountain View',
      'Beach Access',
      'Conference Room',
      'Business Center',
      'Laundry',
      'Pet Friendly'
    ];

    // Filter amenities to only include those that exist in the database
    const availableAmenities = commonAmenities.filter(amenity => 
      allAmenities.has(amenity)
    ).concat(
      // Add any additional amenities from the database that aren't in the common list
      Array.from(allAmenities).filter(amenity => 
        !commonAmenities.includes(amenity)
      )
    );

    return NextResponse.json({
      success: true,
      data: {
        countries: formattedCountries,
        priceRange: {
          min: Math.floor(minPrice),
          max: Math.ceil(maxPrice)
        },
        amenities: availableAmenities.sort(),
        starRatings: Object.keys(starCounts)
          .map(stars => ({
            stars: parseInt(stars),
            count: starCounts[stars]
          }))
          .sort((a, b) => b.stars - a.stars), // Sort by stars descending
        totalHotels: (starRatings || []).length
      }
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
