import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

/**
 * Create a Supabase client for server-side operations
 * This should be used in Server Components, API routes, and Server Actions
 */
export async function createServerSupabaseClient() {
  const cookieStore = await cookies()

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    {
      cookies: {
        get(name) {
          return cookieStore.get(name)?.value
        },
        set(name, value, options) {
          try {
            cookieStore.set({ name, value, ...options })
          } catch (error) {
            // The `set` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
        remove(name, options) {
          try {
            cookieStore.set({ name, value: '', ...options })
          } catch (error) {
            // The `delete` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  )
}

/**
 * Get the current authenticated user session
 * Returns null if no session exists
 */
export async function getServerSession() {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { session }, error } = await supabase.auth.getSession()

    if (error) {
      console.error('Error getting session:', error)
      return null
    }

    return session
  } catch (error) {
    console.error('Error in getServerSession:', error)
    return null
  }
}

/**
 * Get the current authenticated user
 * Returns null if no user is authenticated
 */
export async function getServerUser() {
  try {
    const session = await getServerSession()
    return session?.user || null
  } catch (error) {
    console.error('Error in getServerUser:', error)
    return null
  }
}

/**
 * Check if the current user is an admin
 * Returns false if not authenticated or not an admin
 */
export async function isAdmin() {
  try {
    const user = await getServerUser()
    if (!user) return false

    const supabase = await createServerSupabaseClient()
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('role')
      .eq('user_id', user.id)
      .single()

    if (error) {
      console.error('Error checking admin status:', error)
      return false
    }

    return profile?.role === 'admin'
  } catch (error) {
    console.error('Error in isAdmin:', error)
    return false
  }
}

/**
 * Require admin authentication for server-side operations
 * Throws an error if user is not authenticated or not an admin
 */
export async function requireAdmin() {
  const user = await getServerUser()
  if (!user) {
    throw new Error('Authentication required')
  }

  const adminStatus = await isAdmin()
  if (!adminStatus) {
    throw new Error('Admin privileges required')
  }

  return user
}

/**
 * Get user profile information
 * Returns null if user is not authenticated
 */
export async function getUserProfile() {
  try {
    const user = await getServerUser()
    if (!user) return null

    const supabase = await createServerSupabaseClient()
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('user_id', user.id)
      .single()

    if (error) {
      console.error('Error getting user profile:', error)
      return null
    }

    return profile
  } catch (error) {
    console.error('Error in getUserProfile:', error)
    return null
  }
}

/**
 * Create a browser client for client-side operations
 * This should be used in Client Components
 */
export function createBrowserSupabaseClient() {
  const { createBrowserClient } = require('@supabase/ssr')
  
  return createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  )
}
