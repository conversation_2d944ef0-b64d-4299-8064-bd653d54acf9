"use client";
import { useState } from "react";
import { Card, CardContent } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import { X, Plus, Minus } from "lucide-react";

import { validateLocationUrl } from "@/lib/utils";

export function CountryModal({
  isOpen,
  onClose,
  onSubmit,
  form,
  setForm,
  isEditing,
}) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-full max-w-md mx-4">
        <CardContent className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-2xl font-semibold text-gray-900">
              {isEditing ? "Modifier le Pays" : "Ajouter un Pays"}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          <form onSubmit={onSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nom du pays *
              </label>
              <Input
                type="text"
                value={form.name}
                onChange={(e) => setForm({ ...form, name: e.target.value })}
                placeholder="Ex: Tunisie"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Code pays *
              </label>
              <Input
                type="text"
                value={form.code}
                onChange={(e) =>
                  setForm({ ...form, code: e.target.value.toUpperCase() })
                }
                placeholder="Ex: TUN"
                maxLength={3}
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Devise *
              </label>
              <Input
                type="text"
                value={form.currency}
                onChange={(e) =>
                  setForm({ ...form, currency: e.target.value.toUpperCase() })
                }
                placeholder="Ex: TND"
                maxLength={3}
                required
              />
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="country-featured"
                checked={form.is_featured}
                onChange={(e) =>
                  setForm({ ...form, is_featured: e.target.checked })
                }
                className="mr-2"
              />
              <label
                htmlFor="country-featured"
                className="text-sm text-gray-700"
              >
                Pays vedette
              </label>
            </div>

            <div className="flex justify-end gap-4 pt-4">
              <Button variant="outline" onClick={onClose}>
                Annuler
              </Button>
              <Button variant="primary" type="submit">
                {isEditing ? "Modifier" : "Ajouter"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}

export function CityModal({
  isOpen,
  onClose,
  onSubmit,
  form,
  setForm,
  countries,
  isEditing,
  onAddHighlight,
  onUpdateHighlight,
  onRemoveHighlight,
}) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <CardContent className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-2xl font-semibold text-gray-900">
              {isEditing ? "Modifier la Ville" : "Ajouter une Ville"}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          <form onSubmit={onSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nom de la ville *
                </label>
                <Input
                  type="text"
                  value={form.name}
                  onChange={(e) => setForm({ ...form, name: e.target.value })}
                  placeholder="Ex: Tunis"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Pays *
                </label>
                <select
                  value={form.country_id}
                  onChange={(e) =>
                    setForm({ ...form, country_id: e.target.value })
                  }
                  className="w-full p-2 border border-gray-300 rounded-lg"
                  required
                >
                  <option value="">Sélectionner un pays</option>
                  {countries.map((country) => (
                    <option key={country.id} value={country.id}>
                      {country.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Région
                </label>
                <Input
                  type="text"
                  value={form.region}
                  onChange={(e) => setForm({ ...form, region: e.target.value })}
                  placeholder="Ex: Nord de la Tunisie"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  URL de l'image
                </label>
                <Input
                  type="url"
                  value={form.image_url}
                  onChange={(e) =>
                    setForm({ ...form, image_url: e.target.value })
                  }
                  placeholder="https://..."
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                value={form.description}
                onChange={(e) =>
                  setForm({ ...form, description: e.target.value })
                }
                placeholder="Description de la ville..."
                className="w-full p-2 border border-gray-300 rounded-lg"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Meilleure période
                </label>
                <Input
                  type="text"
                  value={form.best_time}
                  onChange={(e) =>
                    setForm({ ...form, best_time: e.target.value })
                  }
                  placeholder="Ex: Mars - Mai, Septembre - Novembre"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Durée recommandée
                </label>
                <Input
                  type="text"
                  value={form.duration}
                  onChange={(e) =>
                    setForm({ ...form, duration: e.target.value })
                  }
                  placeholder="Ex: 3-4 jours"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Points d'intérêt
              </label>
              {form.highlights.map((highlight, index) => (
                <div key={index} className="flex gap-2 mb-2">
                  <Input
                    type="text"
                    value={highlight}
                    onChange={(e) =>
                      onUpdateHighlight("city", index, e.target.value)
                    }
                    placeholder="Point d'intérêt"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => onRemoveHighlight("city", index)}
                  >
                    <Minus className="w-4 h-4" />
                  </Button>
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => onAddHighlight("city")}
              >
                <Plus className="w-4 h-4 mr-2" />
                Ajouter un point d'intérêt
              </Button>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="city-featured"
                checked={form.is_featured}
                onChange={(e) =>
                  setForm({ ...form, is_featured: e.target.checked })
                }
                className="mr-2"
              />
              <label htmlFor="city-featured" className="text-sm text-gray-700">
                Ville vedette
              </label>
            </div>

            <div className="flex justify-end gap-4 pt-4">
              <Button variant="outline" onClick={onClose}>
                Annuler
              </Button>
              <Button variant="primary" type="submit">
                {isEditing ? "Modifier" : "Ajouter"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}

export function HotelModal({
  isOpen,
  onClose,
  onSubmit,
  form,
  setForm,
  cities,
  isEditing,
  onAddHighlight,
  onUpdateHighlight,
  onRemoveHighlight,
  onAddAmenity,
  onUpdateAmenity,
  onRemoveAmenity,
  hotelId = null,
}) {
  const [locationUrlValidation, setLocationUrlValidation] = useState({
    isValid: true,
    message: "",
  });

  // Handle location URL change with validation
  const handleLocationUrlChange = (url) => {
    setForm({ ...form, location_url: url });
    const validation = validateLocationUrl(url);
    setLocationUrlValidation(validation);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-5xl max-h-[95vh] overflow-hidden flex flex-col shadow-2xl">
        {/* Sticky Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200 bg-white">
          <h2 className="text-2xl font-semibold text-gray-900">
            {isEditing ? "Modifier l'Hôtel" : "Ajouter un Hôtel"}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <CardContent className="p-6 overflow-y-auto flex-1 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
          <form id="hotel-form" onSubmit={onSubmit} className="space-y-6">
            {/* Basic Information Section */}
            <div className="border-b border-gray-200 pb-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Informations de base
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nom de l'hôtel *
                  </label>
                  <Input
                    type="text"
                    value={form.name}
                    onChange={(e) => setForm({ ...form, name: e.target.value })}
                    placeholder="Ex: Radisson Blu Resort"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Ville *
                  </label>
                  <select
                    value={form.city_id}
                    onChange={(e) =>
                      setForm({ ...form, city_id: e.target.value })
                    }
                    className="w-full p-2 border border-gray-300 rounded-lg"
                    required
                  >
                    <option value="">Sélectionner une ville</option>
                    {cities.map((city) => (
                      <option key={city.id} value={city.id}>
                        {city.name} ({city.countries?.name})
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Pricing Information Section */}
            <div className="border-b border-gray-200 pb-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Informations tarifaires
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Étoiles *
                  </label>
                  <select
                    value={form.stars}
                    onChange={(e) =>
                      setForm({ ...form, stars: parseInt(e.target.value) })
                    }
                    className="w-full p-2 border border-gray-300 rounded-lg"
                    required
                  >
                    <option value={1}>1 étoile</option>
                    <option value={2}>2 étoiles</option>
                    <option value={3}>3 étoiles</option>
                    <option value={4}>4 étoiles</option>
                    <option value={5}>5 étoiles</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Prix *
                  </label>
                  <Input
                    type="number"
                    value={form.price}
                    onChange={(e) =>
                      setForm({ ...form, price: e.target.value })
                    }
                    placeholder="19500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Devise *
                  </label>
                  <select
                    value={form.currency}
                    onChange={(e) =>
                      setForm({ ...form, currency: e.target.value })
                    }
                    className="w-full p-2 border border-gray-300 rounded-lg"
                    required
                  >
                    <option value="DA">DA</option>
                    <option value="EUR">EUR</option>
                    <option value="USD">USD</option>
                    <option value="TND">TND</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Media & Contact Section */}
            <div className="border-b border-gray-200 pb-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Médias et contact
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    URL de l'image principale
                  </label>
                  <Input
                    type="url"
                    value={form.image_url}
                    onChange={(e) =>
                      setForm({ ...form, image_url: e.target.value })
                    }
                    placeholder="https://..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Pourcentage de remise
                  </label>
                  <Input
                    type="number"
                    value={form.discount_percentage}
                    onChange={(e) =>
                      setForm({
                        ...form,
                        discount_percentage: parseInt(e.target.value) || 0,
                      })
                    }
                    placeholder="0"
                    min="0"
                    max="100"
                  />
                </div>
              </div>

              {/* Additional Images Section */}
              <div className="border-b border-gray-200 pb-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Images supplémentaires
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Image supplémentaire 1
                    </label>
                    <Input
                      type="url"
                      value={form.image_url_1 || ""}
                      onChange={(e) =>
                        setForm({ ...form, image_url_1: e.target.value })
                      }
                      placeholder="https://..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Image supplémentaire 2
                    </label>
                    <Input
                      type="url"
                      value={form.image_url_2 || ""}
                      onChange={(e) =>
                        setForm({ ...form, image_url_2: e.target.value })
                      }
                      placeholder="https://..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Image supplémentaire 3
                    </label>
                    <Input
                      type="url"
                      value={form.image_url_3 || ""}
                      onChange={(e) =>
                        setForm({ ...form, image_url_3: e.target.value })
                      }
                      placeholder="https://..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Image supplémentaire 4
                    </label>
                    <Input
                      type="url"
                      value={form.image_url_4 || ""}
                      onChange={(e) =>
                        setForm({ ...form, image_url_4: e.target.value })
                      }
                      placeholder="https://..."
                    />
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  value={form.description}
                  onChange={(e) =>
                    setForm({ ...form, description: e.target.value })
                  }
                  placeholder="Description de l'hôtel..."
                  className="w-full p-2 border border-gray-300 rounded-lg"
                  rows={3}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Texte de l'offre
                </label>
                <Input
                  type="text"
                  value={form.offer_text}
                  onChange={(e) =>
                    setForm({ ...form, offer_text: e.target.value })
                  }
                  placeholder="Ex: 1er Enfant -6 ans Gratuit"
                />
              </div>
            </div>

            {/* Contact Information Section */}
            <div className="border-b border-gray-200 pb-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Informations de contact
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Téléphone
                  </label>
                  <Input
                    type="tel"
                    value={form.phone || ""}
                    onChange={(e) =>
                      setForm({ ...form, phone: e.target.value })
                    }
                    placeholder="+216 XX XXX XXX"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email
                  </label>
                  <Input
                    type="email"
                    value={form.email || ""}
                    onChange={(e) =>
                      setForm({ ...form, email: e.target.value })
                    }
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Adresse complète
              </label>
              <textarea
                value={form.address || ""}
                onChange={(e) => setForm({ ...form, address: e.target.value })}
                placeholder="Adresse complète de l'hôtel..."
                className="w-full p-2 border border-gray-300 rounded-lg"
                rows={2}
              />
            </div>

            {/* Location Section */}
            <div className="border-b border-gray-200 pb-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Localisation
              </h3>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  URL de localisation
                </label>
                <Input
                  type="url"
                  value={form.location_url || ""}
                  onChange={(e) => handleLocationUrlChange(e.target.value)}
                  placeholder="https://maps.google.com/... ou https://maps.apple.com/..."
                  className={`${
                    !locationUrlValidation.isValid
                      ? "border-red-500"
                      : locationUrlValidation.isWarning
                      ? "border-yellow-500"
                      : ""
                  }`}
                />
                {locationUrlValidation.message && (
                  <p
                    className={`text-xs mt-1 ${
                      !locationUrlValidation.isValid
                        ? "text-red-600"
                        : locationUrlValidation.isWarning
                        ? "text-yellow-600"
                        : "text-gray-500"
                    }`}
                  >
                    {locationUrlValidation.message}
                  </p>
                )}
                <p className="text-xs text-gray-500 mt-1">
                  Copiez l'URL depuis Google Maps, Apple Maps, ou tout autre
                  service de cartographie
                </p>
              </div>
            </div>

            {/* Check-in/Check-out Times */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Heure d'arrivée
                </label>
                <Input
                  type="time"
                  value={form.check_in_time || "15:00"}
                  onChange={(e) =>
                    setForm({ ...form, check_in_time: e.target.value })
                  }
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Heure de départ
                </label>
                <Input
                  type="time"
                  value={form.check_out_time || "12:00"}
                  onChange={(e) =>
                    setForm({ ...form, check_out_time: e.target.value })
                  }
                />
              </div>
            </div>

            {/* Content & Features Section */}
            <div className="border-b border-gray-200 pb-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Contenu et caractéristiques
              </h3>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Points forts
                </label>
                {form.highlights.map((highlight, index) => (
                  <div key={index} className="flex gap-2 mb-2">
                    <Input
                      type="text"
                      value={highlight}
                      onChange={(e) =>
                        onUpdateHighlight("hotel", index, e.target.value)
                      }
                      placeholder="Point fort"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => onRemoveHighlight("hotel", index)}
                    >
                      <Minus className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => onAddHighlight("hotel")}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Ajouter un point fort
                </Button>
              </div>

              {/* Cloudinary Image Management */}
              <div>
                <p className="text-sm text-gray-600 mb-4">
                  Les images sont maintenant gérées via les champs URL
                  ci-dessus. Utilisez les 5 champs d'URL pour définir l'image
                  principale et les 4 images supplémentaires.
                </p>
              </div>

              {/* Basic Amenities */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Équipements de base
                </label>
                {form.amenities.map((amenity, index) => (
                  <div key={index} className="flex gap-2 mb-2">
                    <Input
                      type="text"
                      value={amenity}
                      onChange={(e) => onUpdateAmenity(index, e.target.value)}
                      placeholder="Équipement"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => onRemoveAmenity(index)}
                    >
                      <Minus className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={onAddAmenity}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Ajouter un équipement
                </Button>
              </div>

              {/* Property Features */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Caractéristiques de la propriété
                </label>
                {(form.property_features || []).map((feature, index) => (
                  <div key={index} className="flex gap-2 mb-2">
                    <Input
                      type="text"
                      value={feature}
                      onChange={(e) => {
                        const newFeatures = [...(form.property_features || [])];
                        newFeatures[index] = e.target.value;
                        setForm({ ...form, property_features: newFeatures });
                      }}
                      placeholder="Ex: WiFi gratuit, Piscine extérieure"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const newFeatures = (
                          form.property_features || []
                        ).filter((_, i) => i !== index);
                        setForm({ ...form, property_features: newFeatures });
                      }}
                    >
                      <Minus className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newFeatures = [...(form.property_features || []), ""];
                    setForm({ ...form, property_features: newFeatures });
                  }}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Ajouter une caractéristique
                </Button>
              </div>

              {/* Dining & Entertainment */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Restauration et divertissement
                </label>
                {(form.dining_entertainment || []).map((item, index) => (
                  <div key={index} className="flex gap-2 mb-2">
                    <Input
                      type="text"
                      value={item}
                      onChange={(e) => {
                        const newItems = [...(form.dining_entertainment || [])];
                        newItems[index] = e.target.value;
                        setForm({ ...form, dining_entertainment: newItems });
                      }}
                      placeholder="Ex: Restaurant principal, Bar de la piscine"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const newItems = (
                          form.dining_entertainment || []
                        ).filter((_, i) => i !== index);
                        setForm({ ...form, dining_entertainment: newItems });
                      }}
                    >
                      <Minus className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newItems = [...(form.dining_entertainment || []), ""];
                    setForm({ ...form, dining_entertainment: newItems });
                  }}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Ajouter un service
                </Button>
              </div>

              {/* Activities & Recreation */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Activités et loisirs
                </label>
                {(form.activities_recreation || []).map((activity, index) => (
                  <div key={index} className="flex gap-2 mb-2">
                    <Input
                      type="text"
                      value={activity}
                      onChange={(e) => {
                        const newActivities = [
                          ...(form.activities_recreation || []),
                        ];
                        newActivities[index] = e.target.value;
                        setForm({
                          ...form,
                          activities_recreation: newActivities,
                        });
                      }}
                      placeholder="Ex: Sports nautiques, Tennis"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const newActivities = (
                          form.activities_recreation || []
                        ).filter((_, i) => i !== index);
                        setForm({
                          ...form,
                          activities_recreation: newActivities,
                        });
                      }}
                    >
                      <Minus className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newActivities = [
                      ...(form.activities_recreation || []),
                      "",
                    ];
                    setForm({ ...form, activities_recreation: newActivities });
                  }}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Ajouter une activité
                </Button>
              </div>

              {/* Special Notices */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Avis spéciaux
                </label>
                <textarea
                  value={form.special_notices || ""}
                  onChange={(e) =>
                    setForm({ ...form, special_notices: e.target.value })
                  }
                  placeholder="Ex: Réservez avant le 15/06/25 pour économiser jusqu'à 200€!"
                  className="w-full p-2 border border-gray-300 rounded-lg"
                  rows={3}
                />
              </div>

              {/* Policies */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Politiques de l'hôtel
                </label>
                <textarea
                  value={form.policies || ""}
                  onChange={(e) =>
                    setForm({ ...form, policies: e.target.value })
                  }
                  placeholder="Ex: Animaux non admis. Pièce d'identité requise à l'arrivée..."
                  className="w-full p-2 border border-gray-300 rounded-lg"
                  rows={3}
                />
              </div>
            </div>

            {/* Settings Section */}
            <div className="border-b border-gray-200 pb-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Paramètres
              </h3>
              <div className="flex gap-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="hotel-featured"
                    checked={form.is_featured}
                    onChange={(e) =>
                      setForm({ ...form, is_featured: e.target.checked })
                    }
                    className="mr-2"
                  />
                  <label
                    htmlFor="hotel-featured"
                    className="text-sm text-gray-700"
                  >
                    Hôtel vedette
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="hotel-active"
                    checked={form.is_active}
                    onChange={(e) =>
                      setForm({ ...form, is_active: e.target.checked })
                    }
                    className="mr-2"
                  />
                  <label
                    htmlFor="hotel-active"
                    className="text-sm text-gray-700"
                  >
                    Hôtel actif
                  </label>
                </div>
              </div>
            </div>
          </form>
        </CardContent>

        {/* Sticky Footer */}
        <div className="flex justify-end gap-4 p-6 border-t border-gray-200 bg-gray-50">
          <Button variant="outline" onClick={onClose}>
            Annuler
          </Button>
          <Button variant="primary" type="submit" form="hotel-form">
            {isEditing ? "Modifier" : "Ajouter"}
          </Button>
        </div>
      </Card>
    </div>
  );
}
