import { NextResponse } from 'next/server';
import { createServerSupabaseClient, requireAdmin } from '@/lib/auth';

export async function GET(request) {
  try {
    // Require admin authentication
    await requireAdmin();
    
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') || 'all';
    const limit = parseInt(searchParams.get('limit')) || 50;
    const offset = parseInt(searchParams.get('offset')) || 0;

    const supabase = await createServerSupabaseClient();
    
    let query = supabase
      .from('bookings')
      .select(`
        *,
        customers (
          first_name,
          last_name,
          email,
          phone
        ),
        hotels (
          name,
          cities (
            name,
            countries (
              name
            )
          )
        )
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (status && status !== 'all') {
      query = query.eq('status', status);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching bookings:', error);
      return NextResponse.json(
        { error: 'Failed to fetch bookings' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: data || []
    });

  } catch (error) {
    console.error('API error:', error);
    
    if (error.message === 'Authentication required' || error.message === 'Admin privileges required') {
      return NextResponse.json(
        { error: error.message },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
