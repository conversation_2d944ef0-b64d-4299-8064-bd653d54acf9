// Environment variable validation utility
export function validateEnvironment() {
  const requiredEnvVars = {
    'NEXT_PUBLIC_SUPABASE_URL': process.env.NEXT_PUBLIC_SUPABASE_URL,
    'NEXT_PUBLIC_SUPABASE_ANON_KEY': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  };

  const missing = [];
  const present = [];

  for (const [key, value] of Object.entries(requiredEnvVars)) {
    if (!value) {
      missing.push(key);
    } else {
      present.push({ key, value: value.substring(0, 20) + '...' });
    }
  }

  return {
    isValid: missing.length === 0,
    missing,
    present,
    report: () => {
      console.log('🔍 Environment Variables Check:');
      console.log('✅ Present:', present);
      if (missing.length > 0) {
        console.log('❌ Missing:', missing);
        console.log('\n📝 To fix this:');
        console.log('1. Create/update your .env file in the project root');
        console.log('2. Add the missing variables:');
        missing.forEach(key => {
          console.log(`   ${key}=your_value_here`);
        });
        console.log('3. Restart your development server');
        console.log('\n🔗 Get your Supabase credentials from:');
        console.log('   https://app.supabase.com/project/gxrzbqazidwoojlkdtag/settings/api');
      }
    }
  };
}

// Debug function to check environment in browser console
export function debugEnvironment() {
  if (typeof window !== 'undefined') {
    console.log('🌐 Client-side Environment Check:');
    console.log('NEXT_PUBLIC_SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL ? '✅ Set' : '❌ Missing');
    console.log('NEXT_PUBLIC_SUPABASE_ANON_KEY:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing');
  } else {
    console.log('🖥️ Server-side Environment Check:');
    console.log('NEXT_PUBLIC_SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL ? '✅ Set' : '❌ Missing');
    console.log('NEXT_PUBLIC_SUPABASE_ANON_KEY:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing');
    console.log('supabaseUrl (legacy):', process.env.supabaseUrl ? '✅ Set' : '❌ Missing');
    console.log('supabaseKey (legacy):', process.env.supabaseKey ? '✅ Set' : '❌ Missing');
  }
}
