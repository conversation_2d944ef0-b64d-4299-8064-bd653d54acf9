"use client";
import { useState } from "react";
import { Card, CardContent } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import { Calendar, Users, Mail, Phone, MapPin, CreditCard } from "lucide-react";
import { createGuestBooking } from "@/lib/guest-booking";

export default function GuestBookingForm({ hotel, onBookingComplete, onCancel }) {
  const [formData, setFormData] = useState({
    // Guest Information
    customer_first_name: '',
    customer_last_name: '',
    customer_email: '',
    customer_phone: '',
    customer_address: '',
    customer_city: '',
    customer_country: '',
    newsletter_subscription: false,
    
    // Booking Details
    check_in_date: '',
    check_out_date: '',
    adults: 1,
    children: 0,
    rooms: 1,
    special_requests: '',
    
    // Hotel Information (from props)
    hotel_id: hotel?.id,
    total_amount: hotel?.price || 0,
    currency: hotel?.currency || 'DA'
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});

  const validateForm = () => {
    const newErrors = {};

    // Required fields validation
    if (!formData.customer_first_name.trim()) {
      newErrors.customer_first_name = 'Le prénom est requis';
    }
    if (!formData.customer_last_name.trim()) {
      newErrors.customer_last_name = 'Le nom est requis';
    }
    if (!formData.customer_email.trim()) {
      newErrors.customer_email = 'L\'email est requis';
    } else if (!/\S+@\S+\.\S+/.test(formData.customer_email)) {
      newErrors.customer_email = 'Format d\'email invalide';
    }
    if (!formData.customer_phone.trim()) {
      newErrors.customer_phone = 'Le téléphone est requis';
    }
    if (!formData.check_in_date) {
      newErrors.check_in_date = 'La date d\'arrivée est requise';
    }
    if (!formData.check_out_date) {
      newErrors.check_out_date = 'La date de départ est requise';
    }

    // Date validation
    if (formData.check_in_date && formData.check_out_date) {
      const checkIn = new Date(formData.check_in_date);
      const checkOut = new Date(formData.check_out_date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (checkIn < today) {
        newErrors.check_in_date = 'La date d\'arrivée ne peut pas être dans le passé';
      }
      if (checkOut <= checkIn) {
        newErrors.check_out_date = 'La date de départ doit être après la date d\'arrivée';
      }
    }

    // Numeric validation
    if (formData.adults < 1) {
      newErrors.adults = 'Au moins 1 adulte est requis';
    }
    if (formData.rooms < 1) {
      newErrors.rooms = 'Au moins 1 chambre est requise';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const calculateTotalAmount = () => {
    if (!formData.check_in_date || !formData.check_out_date || !hotel?.price) {
      return 0;
    }

    const checkIn = new Date(formData.check_in_date);
    const checkOut = new Date(formData.check_out_date);
    const nights = Math.ceil((checkOut - checkIn) / (1000 * 60 * 60 * 24));
    
    return nights * hotel.price * formData.rooms;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const bookingData = {
        ...formData,
        total_amount: calculateTotalAmount()
      };

      const { data, error } = await createGuestBooking(bookingData);

      if (error) {
        throw new Error(error.message);
      }

      // Call the completion callback with booking data
      onBookingComplete(data);

    } catch (error) {
      console.error('Booking error:', error);
      alert(`Erreur lors de la réservation: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const totalAmount = calculateTotalAmount();
  const nights = formData.check_in_date && formData.check_out_date ? 
    Math.ceil((new Date(formData.check_out_date) - new Date(formData.check_in_date)) / (1000 * 60 * 60 * 24)) : 0;

  return (
    <div className="max-w-4xl mx-auto p-6">
      <Card>
        <CardContent className="p-8">
          <div className="mb-6">
            <h2 className="text-3xl font-bold text-gray-900 mb-2">Réservation</h2>
            <p className="text-gray-600">
              Complétez vos informations pour réserver {hotel?.name}
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Guest Information */}
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Users className="w-5 h-5" />
                Informations du client
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Input
                    label="Prénom *"
                    value={formData.customer_first_name}
                    onChange={(e) => handleInputChange('customer_first_name', e.target.value)}
                    error={errors.customer_first_name}
                    placeholder="Votre prénom"
                  />
                </div>
                
                <div>
                  <Input
                    label="Nom *"
                    value={formData.customer_last_name}
                    onChange={(e) => handleInputChange('customer_last_name', e.target.value)}
                    error={errors.customer_last_name}
                    placeholder="Votre nom"
                  />
                </div>
                
                <div>
                  <Input
                    label="Email *"
                    type="email"
                    value={formData.customer_email}
                    onChange={(e) => handleInputChange('customer_email', e.target.value)}
                    error={errors.customer_email}
                    placeholder="<EMAIL>"
                    icon={<Mail className="w-4 h-4" />}
                  />
                </div>
                
                <div>
                  <Input
                    label="Téléphone *"
                    type="tel"
                    value={formData.customer_phone}
                    onChange={(e) => handleInputChange('customer_phone', e.target.value)}
                    error={errors.customer_phone}
                    placeholder="+213 123 456 789"
                    icon={<Phone className="w-4 h-4" />}
                  />
                </div>
                
                <div className="md:col-span-2">
                  <Input
                    label="Adresse"
                    value={formData.customer_address}
                    onChange={(e) => handleInputChange('customer_address', e.target.value)}
                    placeholder="Votre adresse complète"
                    icon={<MapPin className="w-4 h-4" />}
                  />
                </div>
                
                <div>
                  <Input
                    label="Ville"
                    value={formData.customer_city}
                    onChange={(e) => handleInputChange('customer_city', e.target.value)}
                    placeholder="Votre ville"
                  />
                </div>
                
                <div>
                  <Input
                    label="Pays"
                    value={formData.customer_country}
                    onChange={(e) => handleInputChange('customer_country', e.target.value)}
                    placeholder="Votre pays"
                  />
                </div>
              </div>
              
              <div className="mt-4">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={formData.newsletter_subscription}
                    onChange={(e) => handleInputChange('newsletter_subscription', e.target.checked)}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm text-gray-700">
                    Je souhaite recevoir les offres spéciales par email
                  </span>
                </label>
              </div>
            </div>

            {/* Booking Details */}
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                Détails de la réservation
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <Input
                    label="Date d'arrivée *"
                    type="date"
                    value={formData.check_in_date}
                    onChange={(e) => handleInputChange('check_in_date', e.target.value)}
                    error={errors.check_in_date}
                    min={new Date().toISOString().split('T')[0]}
                  />
                </div>
                
                <div>
                  <Input
                    label="Date de départ *"
                    type="date"
                    value={formData.check_out_date}
                    onChange={(e) => handleInputChange('check_out_date', e.target.value)}
                    error={errors.check_out_date}
                    min={formData.check_in_date || new Date().toISOString().split('T')[0]}
                  />
                </div>
                
                <div>
                  <Input
                    label="Adultes *"
                    type="number"
                    value={formData.adults}
                    onChange={(e) => handleInputChange('adults', parseInt(e.target.value) || 1)}
                    error={errors.adults}
                    min="1"
                    max="10"
                  />
                </div>
                
                <div>
                  <Input
                    label="Enfants"
                    type="number"
                    value={formData.children}
                    onChange={(e) => handleInputChange('children', parseInt(e.target.value) || 0)}
                    min="0"
                    max="10"
                  />
                </div>
              </div>
              
              <div className="mt-4">
                <Input
                  label="Nombre de chambres *"
                  type="number"
                  value={formData.rooms}
                  onChange={(e) => handleInputChange('rooms', parseInt(e.target.value) || 1)}
                  error={errors.rooms}
                  min="1"
                  max="10"
                  className="max-w-xs"
                />
              </div>
              
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Demandes spéciales
                </label>
                <textarea
                  value={formData.special_requests}
                  onChange={(e) => handleInputChange('special_requests', e.target.value)}
                  placeholder="Toute demande particulière..."
                  className="w-full p-3 border border-gray-300 rounded-lg"
                  rows={3}
                />
              </div>
            </div>

            {/* Booking Summary */}
            {totalAmount > 0 && (
              <div className="bg-gray-50 p-6 rounded-lg">
                <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <CreditCard className="w-5 h-5" />
                  Résumé de la réservation
                </h3>
                
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Hôtel:</span>
                    <span className="font-medium">{hotel?.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Période:</span>
                    <span>{nights} nuit(s)</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Chambres:</span>
                    <span>{formData.rooms}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Voyageurs:</span>
                    <span>{formData.adults} adulte(s){formData.children > 0 && `, ${formData.children} enfant(s)`}</span>
                  </div>
                  <hr className="my-2" />
                  <div className="flex justify-between text-lg font-bold">
                    <span>Total:</span>
                    <span>{totalAmount.toLocaleString()} {formData.currency}</span>
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-4 justify-end">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Annuler
              </Button>
              <Button
                type="submit"
                variant="primary"
                loading={isSubmitting}
                disabled={totalAmount === 0}
              >
                {isSubmitting ? 'Réservation en cours...' : 'Confirmer la réservation'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
