"use client";
import { useState } from "react";
import Image from "next/image";
import { Card, CardContent } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import { Image as ImageIcon, Save } from "lucide-react";

export default function LandingPageManagement() {
  const [landingPageContent, setLandingPageContent] = useState({
    heroImage: "/images/landingpagebackground.svg",
    heroTitle: "Welcome to Bouguerrouche Travel",
    heroSubtitle: "Your trusted travel partner in Algeria",
  });

  const [isEditing, setIsEditing] = useState(false);

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Here you would typically upload the image to your server/storage
      // and get back the URL. For now, we'll use a local URL
      const imageUrl = URL.createObjectURL(file);
      setLandingPageContent({
        ...landingPageContent,
        heroImage: imageUrl,
      });
    }
  };

  const handleSave = () => {
    // Here you would typically save the changes to your backend
    setIsEditing(false);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container-custom">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Landing Page Management
          </h1>
          <Button
            variant="primary"
            onClick={() => setIsEditing(!isEditing)}
            icon={
              isEditing ? (
                <Save className="w-4 h-4" />
              ) : (
                <ImageIcon className="w-4 h-4" />
              )
            }
          >
            {isEditing ? "Save Changes" : "Edit Content"}
          </Button>
        </div>

        <div className="grid gap-8">
          {/* Hero Section */}
          <Card>
            <CardContent className="p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Hero Section
              </h2>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Hero Image
                  </label>
                  <div className="flex items-center gap-4">
                    <div className="relative w-32 h-32">
                      <Image
                        src={landingPageContent.heroImage}
                        alt="Hero background"
                        width={128}
                        height={128}
                        className="w-full h-full object-cover rounded-lg"
                      />
                    </div>
                    {isEditing && (
                      <Input
                        type="file"
                        accept="image/*"
                        onChange={handleImageChange}
                        className="flex-1"
                      />
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Hero Title
                  </label>
                  {isEditing ? (
                    <Input
                      type="text"
                      value={landingPageContent.heroTitle}
                      onChange={(e) =>
                        setLandingPageContent({
                          ...landingPageContent,
                          heroTitle: e.target.value,
                        })
                      }
                    />
                  ) : (
                    <p className="text-lg text-gray-900">
                      {landingPageContent.heroTitle}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Hero Subtitle
                  </label>
                  {isEditing ? (
                    <Input
                      type="text"
                      value={landingPageContent.heroSubtitle}
                      onChange={(e) =>
                        setLandingPageContent({
                          ...landingPageContent,
                          heroSubtitle: e.target.value,
                        })
                      }
                    />
                  ) : (
                    <p className="text-lg text-gray-600">
                      {landingPageContent.heroSubtitle}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Preview Section */}
          <Card>
            <CardContent className="p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Preview
              </h2>
              <div className="relative h-[40vh] w-full overflow-hidden rounded-lg">
                <Image
                  src={landingPageContent.heroImage}
                  alt="Hero background"
                  fill
                  className="absolute inset-0 w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                  <div className="text-center text-white">
                    <h1 className="text-4xl font-bold mb-2">
                      {landingPageContent.heroTitle}
                    </h1>
                    <p className="text-xl">{landingPageContent.heroSubtitle}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
