// Server-side Cloudinary configuration (only for API routes)
let cloudinary;

if (typeof window === 'undefined') {
  // Only import and configure on server-side
  const { v2 } = require('cloudinary');
  cloudinary = v2;

  cloudinary.config({
    cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
    api_key: process.env.CLOUDINARY_API_KEY,
    api_secret: process.env.CLOUDINARY_API_SECRET,
    secure: true
  });
}

/**
 * Upload image to Cloudinary (Server-side only)
 * @param {File|string} file - File object or base64 string
 * @param {Object} options - Upload options
 * @returns {Promise<Object>} Upload result
 */
export const uploadToCloudinary = async (file, options = {}) => {
  if (typeof window !== 'undefined') {
    throw new Error('uploadToCloudinary can only be called on the server side');
  }

  try {
    const uploadOptions = {
      folder: options.folder || 'bouguerrouche-travel',
      transformation: [
        { quality: 'auto:good' },
        { fetch_format: 'auto' },
        { width: options.width || 1920, height: options.height || 1080, crop: 'limit' }
      ],
      tags: options.tags || ['hotel', 'travel'],
      ...options
    };

    let uploadData;

    if (typeof file === 'string') {
      // Base64 or URL upload
      uploadData = await cloudinary.uploader.upload(file, uploadOptions);
    } else {
      // File upload - convert to base64
      const base64 = await fileToBase64(file);
      uploadData = await cloudinary.uploader.upload(base64, uploadOptions);
    }

    return {
      success: true,
      data: {
        public_id: uploadData.public_id,
        url: uploadData.secure_url,
        width: uploadData.width,
        height: uploadData.height,
        format: uploadData.format,
        bytes: uploadData.bytes,
        created_at: uploadData.created_at
      }
    };
  } catch (error) {
    console.error('Cloudinary upload error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Delete image from Cloudinary (Server-side only)
 * @param {string} publicId - Cloudinary public ID
 * @returns {Promise<Object>} Delete result
 */
export const deleteFromCloudinary = async (publicId) => {
  if (typeof window !== 'undefined') {
    throw new Error('deleteFromCloudinary can only be called on the server side');
  }

  try {
    const result = await cloudinary.uploader.destroy(publicId);
    return {
      success: result.result === 'ok',
      data: result
    };
  } catch (error) {
    console.error('Cloudinary delete error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Generate optimized Cloudinary URL (Client-safe)
 * @param {string} publicId - Cloudinary public ID
 * @param {Object} options - Transformation options
 * @returns {string} Optimized URL
 */
export const getOptimizedImageUrl = (publicId, options = {}) => {
  if (!publicId) return '/images/placeholder.jpg';

  const cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME;
  if (!cloudName) return '/images/placeholder.jpg';

  // Build transformation string
  const transformations = [];

  // Quality optimization
  transformations.push(`q_${options.quality || 'auto:good'}`);

  // Format optimization
  transformations.push('f_auto');

  // Responsive sizing
  if (options.width || options.height) {
    const crop = options.crop || 'fill';
    const gravity = options.gravity || 'auto';

    if (options.width) transformations.push(`w_${options.width}`);
    if (options.height) transformations.push(`h_${options.height}`);
    transformations.push(`c_${crop}`);
    if (gravity !== 'auto') transformations.push(`g_${gravity}`);
  }

  // Additional transformations
  if (options.blur) transformations.push(`e_blur:${options.blur}`);
  if (options.grayscale) transformations.push('e_grayscale');

  const transformationString = transformations.join(',');

  return `https://res.cloudinary.com/${cloudName}/image/upload/${transformationString}/${publicId}`;
};

/**
 * Generate responsive image URLs for different screen sizes
 * @param {string} publicId - Cloudinary public ID
 * @returns {Object} Object with different sized URLs
 */
export const getResponsiveImageUrls = (publicId) => {
  if (!publicId) {
    const placeholder = '/images/placeholder.jpg';
    return {
      thumbnail: placeholder,
      small: placeholder,
      medium: placeholder,
      large: placeholder,
      original: placeholder
    };
  }

  return {
    thumbnail: getOptimizedImageUrl(publicId, { width: 150, height: 150 }),
    small: getOptimizedImageUrl(publicId, { width: 400, height: 300 }),
    medium: getOptimizedImageUrl(publicId, { width: 800, height: 600 }),
    large: getOptimizedImageUrl(publicId, { width: 1200, height: 900 }),
    original: getOptimizedImageUrl(publicId, { width: 1920, height: 1440 })
  };
};

/**
 * Convert File to base64
 * @param {File} file - File object
 * @returns {Promise<string>} Base64 string
 */
const fileToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
};

/**
 * Validate image file
 * @param {File} file - File object
 * @returns {Object} Validation result
 */
export const validateImageFile = (file) => {
  const maxSize = 10 * 1024 * 1024; // 10MB
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'Type de fichier non supporté. Utilisez JPG, PNG ou WebP.'
    };
  }

  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'Fichier trop volumineux. Maximum 10MB.'
    };
  }

  return { valid: true };
};

/**
 * Test Cloudinary connection (Server-side only)
 * @returns {Promise<Object>} Connection test result
 */
export const testCloudinaryConnection = async () => {
  if (typeof window !== 'undefined') {
    throw new Error('testCloudinaryConnection can only be called on the server side');
  }

  try {
    // Test by uploading a small test image
    const testImageData = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';

    const result = await cloudinary.uploader.upload(testImageData, {
      folder: 'bouguerrouche-travel/test',
      public_id: 'connection-test',
      overwrite: true,
      resource_type: 'image'
    });

    // Clean up test image
    await cloudinary.uploader.destroy(result.public_id);

    return {
      success: true,
      message: 'Cloudinary connection successful',
      cloudName: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME
    };
  } catch (error) {
    console.error('Cloudinary connection test failed:', error);
    return {
      success: false,
      error: error.message,
      cloudName: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME
    };
  }
};

/**
 * Get Cloudinary configuration status
 * @returns {Object} Configuration status
 */
export const getCloudinaryConfig = () => {
  return {
    cloudName: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
    hasApiKey: !!process.env.CLOUDINARY_API_KEY,
    hasApiSecret: !!process.env.CLOUDINARY_API_SECRET,
    hasCloudinaryUrl: !!process.env.CLOUDINARY_URL,
    isConfigured: !!(
      process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME &&
      process.env.CLOUDINARY_API_KEY &&
      process.env.CLOUDINARY_API_SECRET
    )
  };
};

// Only export cloudinary instance on server-side
export default typeof window === 'undefined' ? cloudinary : null;
