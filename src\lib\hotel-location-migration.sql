-- Hotel Location Migration Script
-- This script migrates from longitude/latitude coordinates to location_url field
-- Run this in your Supabase SQL Editor

-- Step 1: Add location_url column if it doesn't exist
ALTER TABLE hotels ADD COLUMN IF NOT EXISTS location_url TEXT;

-- Step 2: Remove longitude and latitude columns if they exist
-- (Only run these if you have existing longitude/latitude columns)
-- ALTER TABLE hotels DROP COLUMN IF EXISTS longitude;
-- ALTER TABLE hotels DROP COLUMN IF EXISTS latitude;

-- Step 3: Add comment to document the location_url field
COMMENT ON COLUMN hotels.location_url IS 'Google Maps, Apple Maps, or other mapping service URL for hotel location';

-- Step 4: Create index for location_url if needed for search functionality
-- CREATE INDEX IF NOT EXISTS idx_hotels_location_url ON hotels(location_url) WHERE location_url IS NOT NULL;

-- Step 5: Sample data migration (if you have existing coordinate data)
-- This is an example of how you might migrate existing coordinate data to Google Maps URLs
-- UPDATE hotels 
-- SET location_url = CONCAT(
--   'https://maps.google.com/?q=',
--   latitude::text,
--   ',',
--   longitude::text
-- )
-- WHERE latitude IS NOT NULL 
--   AND longitude IS NOT NULL 
--   AND location_url IS NULL;

-- Step 6: Verify the migration
-- SELECT 
--   id,
--   name,
--   location_url,
--   -- latitude,  -- uncomment if column still exists
--   -- longitude  -- uncomment if column still exists
-- FROM hotels 
-- WHERE location_url IS NOT NULL
-- LIMIT 10;

-- Step 7: Add validation constraint (optional)
-- This ensures location_url is either NULL or a valid URL format
-- ALTER TABLE hotels ADD CONSTRAINT check_location_url_format 
-- CHECK (
--   location_url IS NULL OR 
--   location_url ~ '^https?://[^\s/$.?#].[^\s]*$'
-- );
