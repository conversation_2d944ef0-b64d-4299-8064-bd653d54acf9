"use client";

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { useToast } from '@/components/ui/Toast';
import { 
  CheckCircle, 
  Clock, 
  XCircle, 
  AlertCircle,
  CreditCard,
  RefreshCw
} from 'lucide-react';
import { BOOKING_STATUSES, PAYMENT_STATUSES, isValidStatusTransition } from '@/lib/booking-status-client';

const STATUS_COLORS = {
  [BOOKING_STATUSES.PENDING]: 'bg-yellow-100 text-yellow-800',
  [BOOKING_STATUSES.CONFIRMED]: 'bg-blue-100 text-blue-800',
  [BOOKING_STATUSES.COMPLETED]: 'bg-green-100 text-green-800',
  [BOOKING_STATUSES.CANCELLED]: 'bg-red-100 text-red-800'
};

const PAYMENT_STATUS_COLORS = {
  [PAYMENT_STATUSES.PENDING]: 'bg-yellow-100 text-yellow-800',
  [PAYMENT_STATUSES.PAID]: 'bg-green-100 text-green-800',
  [PAYMENT_STATUSES.REFUNDED]: 'bg-purple-100 text-purple-800',
  [PAYMENT_STATUSES.FAILED]: 'bg-red-100 text-red-800'
};

const STATUS_ICONS = {
  [BOOKING_STATUSES.PENDING]: Clock,
  [BOOKING_STATUSES.CONFIRMED]: CheckCircle,
  [BOOKING_STATUSES.COMPLETED]: CheckCircle,
  [BOOKING_STATUSES.CANCELLED]: XCircle
};

export default function BookingStatusManager({
  booking,
  onStatusUpdate,
  onPaymentStatusUpdate,
  isUpdating = false
}) {
  const [notes, setNotes] = useState('');
  const [showNotes, setShowNotes] = useState(false);
  const { toast } = useToast();

  const handleStatusUpdate = async (newStatus) => {
    if (!isValidStatusTransition(booking.status, newStatus)) {
      toast.error(`Transition invalide de ${booking.status} vers ${newStatus}`);
      return;
    }

    try {
      await onStatusUpdate(booking.id, newStatus, notes);
      setNotes('');
      setShowNotes(false);
    } catch (error) {
      console.error('Error updating status:', error);
      toast.error('Erreur lors de la mise à jour du statut. Veuillez réessayer.');
    }
  };

  const handlePaymentStatusUpdate = async (newPaymentStatus) => {
    try {
      await onPaymentStatusUpdate(booking.id, newPaymentStatus, notes);
      setNotes('');
      setShowNotes(false);
    } catch (error) {
      console.error('Error updating payment status:', error);
      toast.error('Erreur lors de la mise à jour du statut de paiement. Veuillez réessayer.');
    }
  };

  const getAvailableStatusTransitions = () => {
    const transitions = [];
    Object.values(BOOKING_STATUSES).forEach(status => {
      if (status !== booking.status && isValidStatusTransition(booking.status, status)) {
        transitions.push(status);
      }
    });
    return transitions;
  };

  const StatusIcon = STATUS_ICONS[booking.status] || AlertCircle;

  return (
    <Card>
      <CardContent className="p-6">
        <div className="space-y-6">
          {/* Current Status Display */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Booking Status Management
            </h3>
            
            <div className="flex items-center space-x-4 mb-4">
              <div className="flex items-center space-x-2">
                <StatusIcon className="w-5 h-5" />
                <span className="font-medium">Current Status:</span>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${STATUS_COLORS[booking.status]}`}>
                  {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                </span>
              </div>
              
              <div className="flex items-center space-x-2">
                <CreditCard className="w-5 h-5" />
                <span className="font-medium">Payment:</span>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${PAYMENT_STATUS_COLORS[booking.payment_status]}`}>
                  {booking.payment_status.charAt(0).toUpperCase() + booking.payment_status.slice(1)}
                </span>
              </div>
            </div>
          </div>

          {/* Status Transition Buttons */}
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-3">
              Update Booking Status
            </h4>
            
            <div className="flex flex-wrap gap-2 mb-4">
              {getAvailableStatusTransitions().map(status => (
                <Button
                  key={status}
                  variant="outline"
                  size="sm"
                  onClick={() => handleStatusUpdate(status)}
                  disabled={isUpdating}
                  className="capitalize"
                >
                  {isUpdating ? <RefreshCw className="w-4 h-4 animate-spin mr-2" /> : null}
                  Mark as {status}
                </Button>
              ))}
            </div>
          </div>

          {/* Payment Status Buttons */}
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-3">
              Update Payment Status
            </h4>
            
            <div className="flex flex-wrap gap-2 mb-4">
              {Object.values(PAYMENT_STATUSES).map(paymentStatus => (
                <Button
                  key={paymentStatus}
                  variant={booking.payment_status === paymentStatus ? "primary" : "outline"}
                  size="sm"
                  onClick={() => handlePaymentStatusUpdate(paymentStatus)}
                  disabled={isUpdating || booking.payment_status === paymentStatus}
                  className="capitalize"
                >
                  {isUpdating ? <RefreshCw className="w-4 h-4 animate-spin mr-2" /> : null}
                  {paymentStatus}
                </Button>
              ))}
            </div>
          </div>

          {/* Notes Section */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-md font-medium text-gray-900">
                Admin Notes
              </h4>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowNotes(!showNotes)}
              >
                {showNotes ? 'Hide Notes' : 'Add Notes'}
              </Button>
            </div>

            {showNotes && (
              <div className="space-y-3">
                <textarea
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Add notes about this status change..."
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  rows={3}
                />
              </div>
            )}

            {booking.admin_notes && (
              <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600">
                  <strong>Previous Notes:</strong> {booking.admin_notes}
                </p>
              </div>
            )}
          </div>

          {/* Booking Timeline */}
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-3">
              Booking Timeline
            </h4>
            
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex justify-between">
                <span>Created:</span>
                <span>{new Date(booking.created_at).toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>Last Updated:</span>
                <span>{new Date(booking.updated_at).toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>Check-in:</span>
                <span>{new Date(booking.check_in_date).toLocaleDateString()}</span>
              </div>
              <div className="flex justify-between">
                <span>Check-out:</span>
                <span>{new Date(booking.check_out_date).toLocaleDateString()}</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
