"use client";
import { useEffect, useState } from "react";
import { getTopHotels } from "@/lib/database";
import {
  Card,
  CardContent,
  CardTitle,
  CardDescription,
} from "@/components/ui/Card";

export default function TopHotelsSection() {
  const [hotels, setHotels] = useState([]);

  useEffect(() => {
    async function fetchHotels() {
      try {
        const data = await getTopHotels();
        console.log("Fetched hotels:", data);
        setHotels(data || []);
      } catch (err) {
        console.error("Error fetching hotels:", err);
      }
    }
    fetchHotels();
  }, []);

  return (
    <section className="px-4 py-12 md:px-12 bg-gray-50 min-h-screen">
      <h2 className="text-3xl font-bold mb-10 text-center text-gray-800">
        Top Hotels
      </h2>

      {hotels.length === 0 ? (
        <p className="text-center text-gray-600">
          No hotels available right now.
        </p>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 justify-items-center">
          {hotels.map((hotel) => (
            <Card
              key={hotel.id}
              className="w-full max-w-sm overflow-hidden shadow-md hover:shadow-xl transition-all"
              padding="none"
            >
              {/* Placeholder for image */}
              <div className="w-full h-48 bg-gray-200 flex items-center justify-center text-gray-500 text-sm">
                Image here
              </div>

              <CardContent className="p-6 space-y-4">
                {/* Discount */}
                {hotel.discount_percentage > 0 && (
                  <span className="bg-red-500 text-white text-xs font-bold px-3 py-1 rounded-full">
                    -{hotel.discount_percentage}% OFF
                  </span>
                )}

                {/* Title */}
                <CardTitle>{hotel.name}</CardTitle>

                {/* Description */}
                <CardDescription>
                  {hotel.description?.slice(0, 100)}...
                </CardDescription>

                {/* Stars */}
                <p className="text-yellow-500 text-sm">
                  ⭐ {hotel.stars} Stars
                </p>

                {/* Price */}
                <p className="text-lg font-semibold text-sky-900">
                  {hotel.price} {hotel.currency}
                </p>

                {/* Offer text */}
                {hotel.offer_text && (
                  <p className="text-sm text-sky-600 italic">
                    {hotel.offer_text}
                  </p>
                )}

                {/* Highlights */}
                {Array.isArray(hotel.highlights) && hotel.highlights.length > 0 && (
                  <div className="flex flex-wrap gap-2 pt-2">
                    {hotel.highlights.map((item, i) => (
                      <span
                        key={i}
                        className="text-xs bg-sky-100 text-sky-800 px-3 py-1 rounded-full"
                      >
                        {item}
                      </span>
                    ))}
                  </div>
                )}

                {/* CTA Button */}
                <div className="pt-4">
                  <button className="bg-sky-900 text-white text-sm font-semibold px-4 py-2 rounded hover:bg-sky-800">
                    DÉCOUVRIR
                  </button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </section>
  );
}
