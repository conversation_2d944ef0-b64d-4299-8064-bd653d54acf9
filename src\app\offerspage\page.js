"use client";
import React, { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import Header from "@/components/header/Header";
import Footer from "@/components/footer/Footer";
import SafeImage from "@/components/ui/SafeImage";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";
import GuestBookingForm from "@/components/booking/GuestBookingForm";
import { Star, MapPin, Wifi, Car, Waves, Calendar, Users, Phone } from "lucide-react";
import { useToast } from "@/components/ui/Toast";

const amenityIcons = {
  'WiFi': Wifi,
  'Parking': Car,
  'Pool': Waves,
  'Piscine': Waves,
  'Wi-Fi': Wifi,
  'Stationnement': Car
};

export default function HotelOfferDetail() {
  const searchParams = useSearchParams();
  const { toast } = useToast();

  const [hotel, setHotel] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [mainImage, setMainImage] = useState("/images/back.png");
  const [showBookingForm, setShowBookingForm] = useState(false);
  const [bookingComplete, setBookingComplete] = useState(false);

  useEffect(() => {
    const loadHotelData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get hotel_id from URL parameters
        const hotelId = searchParams.get('hotel_id');

        if (!hotelId) {
          setError('ID de l\'hôtel manquant');
          return;
        }

        // Fetch hotel data from database
        const response = await fetch(`/api/hotels/${hotelId}`);

        if (!response.ok) {
          if (response.status === 404) {
            setError('Hôtel non trouvé');
          } else {
            setError('Erreur lors du chargement de l\'hôtel');
          }
          return;
        }

        const data = await response.json();

        if (!data.success || !data.data) {
          setError('Données de l\'hôtel non disponibles');
          return;
        }

        setHotel(data.data);
        setMainImage(data.data.images.primary);

      } catch (err) {
        console.error('Error loading hotel data:', err);
        setError('Erreur de connexion');
      } finally {
        setLoading(false);
      }
    };

    loadHotelData();
  }, [searchParams]);

  const handleBookingComplete = (bookingData) => {
    setBookingComplete(true);
    setShowBookingForm(false);
    toast.success('Réservation confirmée avec succès !');
  };

  const getAmenityIcon = (amenity) => {
    const IconComponent = amenityIcons[amenity];
    return IconComponent ? <IconComponent className="w-4 h-4" /> : null;
  };

  if (loading) {
    return (
      <>
        <Header />
        <div className="pt-24 max-w-7xl mx-auto p-4">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <div className="text-lg text-gray-600">Chargement de l&apos;hôtel...</div>
          </div>
        </div>
        <Footer />
      </>
    );
  }

  if (error || !hotel) {
    return (
      <>
        <Header />
        <div className="pt-24 max-w-7xl mx-auto p-4">
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-red-500 text-2xl">⚠️</span>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              {error || 'Hôtel non trouvé'}
            </h2>
            <p className="text-gray-600 mb-6">
              L&apos;hôtel que vous recherchez n&apos;existe pas ou n&apos;est plus disponible.
            </p>
            <Button
              onClick={() => window.history.back()}
              variant="primary"
            >
              Retour
            </Button>
          </div>
        </div>
        <Footer />
      </>
    );
  }

  const location = hotel.cities?.name && hotel.cities?.countries?.name
    ? `${hotel.cities.name}, ${hotel.cities.countries.name}`
    : 'Localisation non spécifiée';

  if (showBookingForm) {
    return (
      <>
        <Header />
        <div className="pt-24 max-w-7xl mx-auto p-4">
          <div className="mb-6">
            <Button
              onClick={() => setShowBookingForm(false)}
              variant="outline"
              className="mb-4"
            >
              ← Retour aux détails de l'hôtel
            </Button>
          </div>
          <GuestBookingForm
            hotel={hotel}
            onBookingComplete={handleBookingComplete}
            onCancel={() => setShowBookingForm(false)}
          />
        </div>
        <Footer />
      </>
    );
  }

  return (
    <>
      <Header />
      <div className="pt-24 max-w-7xl mx-auto p-4">
        {bookingComplete && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2 text-green-800">
              <span className="text-green-500">✓</span>
              <span className="font-medium">Réservation confirmée avec succès !</span>
            </div>
          </div>
        )}

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Left Column - Main Content */}
          <div className="w-full lg:w-2/3 space-y-6">
            {/* Hotel Header */}
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <MapPin className="w-4 h-4" />
                <span>{location}</span>
              </div>
              <h1 className="text-3xl font-bold text-gray-900">{hotel.name}</h1>
              {hotel.stars && (
                <div className="flex items-center gap-1">
                  {[...Array(hotel.stars)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                  ))}
                  <span className="ml-2 text-sm text-gray-600">
                    {hotel.stars} étoile{hotel.stars > 1 ? 's' : ''}
                  </span>
                </div>
              )}
            </div>

            {/* Image Gallery */}
            <div className="space-y-4">
              <div className="rounded-2xl overflow-hidden shadow-md">
                <SafeImage
                  src={mainImage}
                  alt={hotel.name}
                  width={800}
                  height={400}
                  className="w-full h-80 object-cover"
                  priority
                />
              </div>
              {hotel.images?.gallery?.length > 0 && (
                <div className="grid grid-cols-4 gap-2">
                  {hotel.images.gallery.slice(0, 8).map((img, i) => (
                    <SafeImage
                      key={img.id || i}
                      src={img.url}
                      alt={img.alt || `${hotel.name} - Image ${i + 1}`}
                      width={200}
                      height={120}
                      className={`cursor-pointer h-20 w-full object-cover rounded-lg border-2 transition hover:opacity-80 ${
                        mainImage === img.url
                          ? "border-primary"
                          : "border-transparent"
                      }`}
                      onClick={() => setMainImage(img.url)}
                    />
                  ))}
                </div>
              )}
            </div>
            {/* Hotel Description */}
            {hotel.description && (
              <div>
                <h3 className="text-xl font-semibold mb-3">À propos de cet hôtel</h3>
                <p className="text-gray-600 leading-relaxed">
                  {hotel.description}
                </p>
              </div>
            )}

            {/* Special Offer */}
            {hotel.offer_text && (
              <div className="bg-accent/10 border border-accent/20 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-accent-dark mb-2">Offre spéciale</h3>
                <p className="text-accent-dark">
                  {hotel.offer_text}
                </p>
                {hotel.discount_percentage > 0 && (
                  <Badge variant="accent" className="mt-2">
                    Économisez {hotel.discount_percentage}%
                  </Badge>
                )}
              </div>
            )}

            {/* Highlights */}
            {hotel.highlights?.length > 0 && (
              <div>
                <h3 className="text-xl font-semibold mb-3">Points forts</h3>
                <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {hotel.highlights.map((highlight, index) => (
                    <li key={index} className="flex items-center gap-2 text-gray-600">
                      <span className="w-2 h-2 bg-primary rounded-full flex-shrink-0"></span>
                      {highlight}
                    </li>
                  ))}
                </ul>
              </div>
            )}
            {/* Amenities */}
            {hotel.amenities?.length > 0 && (
              <div>
                <h3 className="text-xl font-semibold mb-4">Équipements et services</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {hotel.amenities.map((amenity, index) => (
                    <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                      {getAmenityIcon(amenity)}
                      <span className="text-gray-700">{amenity}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Destination Info */}
            {hotel.cities && (
              <div>
                <h3 className="text-xl font-semibold mb-4">Informations sur la destination</h3>
                <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                  <div className="flex items-center gap-2">
                    <MapPin className="w-5 h-5 text-primary" />
                    <span className="font-medium">{hotel.cities.name}</span>
                    {hotel.cities.region && (
                      <span className="text-gray-600">• {hotel.cities.region}</span>
                    )}
                  </div>
                  {hotel.cities.description && (
                    <p className="text-gray-600">{hotel.cities.description}</p>
                  )}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    {hotel.cities.best_time && (
                      <div>
                        <span className="font-semibold text-gray-700">Meilleure période :</span>
                        <br />
                        <span className="text-gray-600">{hotel.cities.best_time}</span>
                      </div>
                    )}
                    {hotel.cities.duration && (
                      <div>
                        <span className="font-semibold text-gray-700">Durée recommandée :</span>
                        <br />
                        <span className="text-gray-600">{hotel.cities.duration}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Hotel Info */}
            <div>
              <h3 className="text-xl font-semibold mb-4">Informations pratiques</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-primary" />
                  <span>Check-in: 15h00</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-primary" />
                  <span>Check-out: 12h00</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4 text-primary" />
                  <span>Capacité maximale par chambre</span>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4 text-primary" />
                  <span>Service client 24h/24</span>
                </div>
              </div>
            </div>
                    {hotel.location_url && (
  <div className="mt-12 items-start">
    <h3 className="text-xl font-semibold mb-4 text-gray-800">Emplacement sur la carte</h3>
    <div className="w-full max-w-2xl h-72 mx-auto rounded-xl overflow-hidden shadow-lg border border-gray-300">
      <iframe
        src={hotel.location_url.replace("google.com/?q=", "google.com/maps?q=")}
        width="100%"
        height="100%"
        allowFullScreen
        loading="lazy"
        referrerPolicy="no-referrer-when-downgrade"
        className="w-full h-full border-none"
      />
    </div >
  </div>
)}
          </div>

          {/* Right Column - Booking Sidebar */}
          <div className="w-full lg:w-1/3">
            <div className="sticky top-28">
              <div className="rounded-2xl p-6 shadow-xl border border-gray-200 space-y-6 bg-white">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">
                    {hotel.name}
                  </h2>
                  <p className="text-gray-600 flex items-center gap-1">
                    <MapPin className="w-4 h-4" />
                    {location}
                  </p>
                </div>

                {hotel.stars && (
                  <div className="flex items-center gap-1">
                    {[...Array(hotel.stars)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                    ))}
                    <span className="ml-2 text-gray-600">
                      {hotel.stars} étoile{hotel.stars > 1 ? 's' : ''}
                    </span>
                  </div>
                )}

                <div className="border-t border-gray-200 pt-4">
                  <div className="space-y-2">
                    {hotel.discount_percentage > 0 && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500 line-through">
                          Prix normal: {Math.round(hotel.price * (1 + hotel.discount_percentage / 100))} {hotel.currency}
                        </span>
                        <Badge variant="accent">
                          -{hotel.discount_percentage}%
                        </Badge>
                      </div>
                    )}
                    <div className="flex items-baseline gap-2">
                      <span className="text-3xl font-bold text-primary">
                        {hotel.price?.toLocaleString()}
                      </span>
                      <span className="text-lg text-gray-600">{hotel.currency}</span>
                    </div>
                    <p className="text-sm text-gray-500">par nuit</p>
                  </div>
                </div>

                <div className="space-y-3">
                  <Button
                    onClick={() => setShowBookingForm(true)}
                    variant="primary"
                    className="w-full py-3 text-lg font-semibold"
                  >
                    Réserver maintenant
                  </Button>
                  <Button
                    onClick={() => window.history.back()}
                    variant="outline"
                    className="w-full py-2"
                  >
                    Retour aux résultats
                  </Button>
                </div>

                <div className="text-xs text-gray-500 space-y-1">
                  <p>• Annulation gratuite jusqu'à 24h avant l'arrivée</p>
                  <p>• Confirmation immédiate</p>
                  <p>• Aucun frais de réservation</p>
                </div>
                
              </div>
            </div>
          </div>
        </div>
      </div>








      <Footer />
    </>
  );
}
