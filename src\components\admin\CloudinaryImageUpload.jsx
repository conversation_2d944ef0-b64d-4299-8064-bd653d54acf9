"use client";
import { useState, useRef, useCallback } from 'react';
import { Upload, X, Image as ImageIcon, Loader2 } from 'lucide-react';
import Button from '@/components/ui/Button';
import { validateImageFile, uploadImageToCloudinary } from '@/lib/cloudinary-client';

export const CloudinaryImageUpload = ({ 
  onImageUploaded, 
  folder = 'hotels', 
  multiple = false,
  maxFiles = 10,
  className = '' 
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({});
  const fileInputRef = useRef(null);

  const uploadFile = async (file) => {
    return await uploadImageToCloudinary(file, {
      folder: folder,
      tags: ['hotel', 'travel']
    });
  };

  const handleFiles = useCallback(async (files) => {
    const fileArray = Array.from(files);
    
    if (!multiple && fileArray.length > 1) {
      alert('Vous ne pouvez télécharger qu\'un seul fichier à la fois.');
      return;
    }

    if (fileArray.length > maxFiles) {
      alert(`Vous ne pouvez télécharger que ${maxFiles} fichiers maximum.`);
      return;
    }

    setUploading(true);
    const results = [];

    try {
      for (let i = 0; i < fileArray.length; i++) {
        const file = fileArray[i];
        const fileId = `${file.name}-${Date.now()}`;
        
        setUploadProgress(prev => ({
          ...prev,
          [fileId]: { name: file.name, progress: 0 }
        }));

        try {
          // Simulate progress for better UX
          const progressInterval = setInterval(() => {
            setUploadProgress(prev => ({
              ...prev,
              [fileId]: {
                ...prev[fileId],
                progress: Math.min(prev[fileId]?.progress + 10, 90)
              }
            }));
          }, 200);

          const result = await uploadFile(file);
          
          clearInterval(progressInterval);
          setUploadProgress(prev => ({
            ...prev,
            [fileId]: { ...prev[fileId], progress: 100 }
          }));

          if (result.success) {
            results.push({
              ...result.data,
              originalName: file.name
            });
          } else {
            throw new Error(result.error);
          }
        } catch (error) {
          console.error(`Error uploading ${file.name}:`, error);
          setUploadProgress(prev => ({
            ...prev,
            [fileId]: { ...prev[fileId], error: error.message }
          }));
        }
      }

      // Call the callback with results
      if (multiple) {
        onImageUploaded(results);
      } else if (results.length > 0) {
        onImageUploaded(results[0]);
      }

    } finally {
      setUploading(false);
      setTimeout(() => setUploadProgress({}), 2000);
    }
  }, [multiple, maxFiles, folder, onImageUploaded]);

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFiles(files);
    }
  }, [handleFiles]);

  const handleFileSelect = useCallback((e) => {
    const files = e.target.files;
    if (files.length > 0) {
      handleFiles(files);
    }
    // Reset input value to allow selecting the same file again
    e.target.value = '';
  }, [handleFiles]);

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <div
        className={`
          relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer
          transition-colors duration-200
          ${isDragging 
            ? 'border-primary bg-primary/5' 
            : 'border-gray-300 hover:border-primary hover:bg-gray-50'
          }
          ${uploading ? 'pointer-events-none opacity-50' : ''}
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={openFileDialog}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          multiple={multiple}
          onChange={handleFileSelect}
          className="hidden"
        />

        <div className="space-y-4">
          <div className="flex justify-center">
            {uploading ? (
              <Loader2 className="w-12 h-12 text-primary animate-spin" />
            ) : (
              <Upload className="w-12 h-12 text-gray-400" />
            )}
          </div>

          <div>
            <p className="text-lg font-medium text-gray-900">
              {uploading ? 'Téléchargement en cours...' : 'Glissez-déposez vos images ici'}
            </p>
            <p className="text-sm text-gray-500">
              ou cliquez pour sélectionner {multiple ? 'des fichiers' : 'un fichier'}
            </p>
            <p className="text-xs text-gray-400 mt-2">
              JPG, PNG, WebP jusqu'à 10MB {multiple ? `(max ${maxFiles} fichiers)` : ''}
            </p>
          </div>

          {!uploading && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                openFileDialog();
              }}
            >
              <ImageIcon className="w-4 h-4 mr-2" />
              Choisir {multiple ? 'des images' : 'une image'}
            </Button>
          )}
        </div>
      </div>

      {/* Upload Progress */}
      {Object.keys(uploadProgress).length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">Progression du téléchargement</h4>
          {Object.entries(uploadProgress).map(([fileId, progress]) => (
            <div key={fileId} className="bg-gray-50 rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-600 truncate">
                  {progress.name}
                </span>
                {progress.error ? (
                  <span className="text-xs text-red-500">Erreur</span>
                ) : (
                  <span className="text-xs text-gray-500">
                    {progress.progress}%
                  </span>
                )}
              </div>
              
              {progress.error ? (
                <div className="text-xs text-red-500 bg-red-50 p-2 rounded">
                  {progress.error}
                </div>
              ) : (
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-primary h-2 rounded-full transition-all duration-300"
                    style={{ width: `${progress.progress}%` }}
                  />
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default CloudinaryImageUpload;
