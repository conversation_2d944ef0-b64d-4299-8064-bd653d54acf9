/**
 * Client-side Booking Status Management
 * These functions can be used in client components
 */

// Valid booking statuses and their allowed transitions
export const BOOKING_STATUSES = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed', 
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
};

export const PAYMENT_STATUSES = {
  PENDING: 'pending',
  PAID: 'paid',
  REFUNDED: 'refunded',
  FAILED: 'failed'
};

// Define allowed status transitions
const STATUS_TRANSITIONS = {
  [BOOKING_STATUSES.PENDING]: [BOOKING_STATUSES.CONFIRMED, BOOKING_STATUSES.CANCELLED],
  [BOOKING_STATUSES.CONFIRMED]: [BOOKING_STATUSES.COMPLETED, BOOKING_STATUSES.CANCELLED],
  [BOOKING_STATUSES.COMPLETED]: [BOOKING_STATUSES.CANCELLED], // Allow cancellation for refunds
  [BOOKING_STATUSES.CANCELLED]: [] // Terminal state
};

/**
 * Validate if a status transition is allowed
 */
export function isValidStatusTransition(currentStatus, newStatus) {
  if (!currentStatus || !newStatus) return false;
  if (currentStatus === newStatus) return true; // Same status is always valid
  
  const allowedTransitions = STATUS_TRANSITIONS[currentStatus] || [];
  return allowedTransitions.includes(newStatus);
}

/**
 * Client-side function to update booking status via API
 */
export async function updateBookingStatusClient(bookingId, newStatus, notes = null) {
  try {
    const response = await fetch('/api/bookings/update-status', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        bookingId,
        newStatus,
        notes
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update booking status');
    }

    const responseData = await response.json();
    return { data: responseData.data || null, error: null };
  } catch (error) {
    console.error('Error updating booking status:', error);
    return { data: null, error };
  }
}

/**
 * Client-side function to update payment status via API
 */
export async function updatePaymentStatusClient(bookingId, newPaymentStatus, notes = null) {
  try {
    const response = await fetch('/api/bookings/update-payment-status', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        bookingId,
        newPaymentStatus,
        notes
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update payment status');
    }

    const responseData = await response.json();
    return { data: responseData.data || null, error: null };
  } catch (error) {
    console.error('Error updating payment status:', error);
    return { data: null, error };
  }
}

/**
 * Client-side function to get booking status statistics via API
 */
export async function getBookingStatusStatsClient() {
  try {
    const response = await fetch('/api/bookings/stats');

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to get booking stats');
    }

    const responseData = await response.json();
    return { data: responseData.data || null, error: null };
  } catch (error) {
    console.error('Error getting booking stats:', error);
    return { data: null, error };
  }
}

/**
 * Client-side function to get bookings by status via API
 */
export async function getBookingsByStatusClient(status, limit = 50, offset = 0) {
  try {
    const params = new URLSearchParams({
      status: status || 'all',
      limit: limit.toString(),
      offset: offset.toString()
    });

    const response = await fetch(`/api/bookings?${params}`);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to get bookings');
    }

    const responseData = await response.json();
    return { data: responseData.data || [], error: null };
  } catch (error) {
    console.error('Error getting bookings:', error);
    return { data: null, error };
  }
}
