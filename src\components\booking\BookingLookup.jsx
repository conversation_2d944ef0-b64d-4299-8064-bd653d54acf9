"use client";
import { useState } from "react";
import { Card, CardContent } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import { Search, Calendar, MapPin, Users, CreditCard } from "lucide-react";
import { getBookingByReference } from "@/lib/guest-booking";

export default function BookingLookup() {
  const [bookingReference, setBookingReference] = useState('');
  const [booking, setBooking] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSearch = async (e) => {
    e.preventDefault();
    
    if (!bookingReference.trim()) {
      setError('Veuillez entrer un numéro de réservation');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const { data, error: searchError } = await getBookingByReference(bookingReference.trim());
      
      if (searchError) {
        setError('Réservation non trouvée. Vérifiez votre numéro de réservation.');
        setBooking(null);
      } else {
        setBooking(data);
        setError('');
      }
    } catch (error) {
      console.error('Search error:', error);
      setError('Erreur lors de la recherche. Veuillez réessayer.');
      setBooking(null);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const calculateNights = () => {
    if (!booking) return 0;
    const checkIn = new Date(booking.check_in_date);
    const checkOut = new Date(booking.check_out_date);
    return Math.ceil((checkOut - checkIn) / (1000 * 60 * 60 * 24));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'confirmed':
        return 'Confirmé';
      case 'pending':
        return 'En attente';
      case 'cancelled':
        return 'Annulé';
      case 'completed':
        return 'Terminé';
      default:
        return status;
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <Card>
        <CardContent className="p-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Rechercher ma réservation
            </h1>
            <p className="text-gray-600">
              Entrez votre numéro de réservation pour consulter les détails
            </p>
          </div>

          {/* Search Form */}
          <form onSubmit={handleSearch} className="mb-8">
            <div className="flex gap-4">
              <div className="flex-1">
                <Input
                  type="text"
                  value={bookingReference}
                  onChange={(e) => setBookingReference(e.target.value.toUpperCase())}
                  placeholder="Ex: BT1234567890ABCD"
                  icon={<Search className="w-4 h-4" />}
                  error={error}
                />
              </div>
              <Button
                type="submit"
                variant="primary"
                loading={loading}
                disabled={!bookingReference.trim()}
              >
                Rechercher
              </Button>
            </div>
          </form>

          {/* Booking Details */}
          {booking && (
            <div className="space-y-6">
              {/* Header */}
              <div className="bg-green-50 p-6 rounded-lg">
                <div className="flex justify-between items-start">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">
                      Réservation trouvée
                    </h2>
                    <p className="text-lg text-gray-700">
                      Numéro: <span className="font-semibold">{booking.booking_reference}</span>
                    </p>
                  </div>
                  <span className={`px-4 py-2 rounded-full text-sm font-medium ${getStatusColor(booking.status)}`}>
                    {getStatusText(booking.status)}
                  </span>
                </div>
              </div>

              {/* Hotel Information */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <MapPin className="w-5 h-5" />
                  Hôtel
                </h3>
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h4 className="text-lg font-semibold">{booking.hotels?.name}</h4>
                  <p className="text-gray-600">
                    {booking.hotels?.cities?.name}, {booking.hotels?.cities?.countries?.name}
                  </p>
                  <div className="flex items-center gap-1 mt-2">
                    {[...Array(booking.hotels?.stars || 0)].map((_, i) => (
                      <span key={i} className="text-yellow-400">★</span>
                    ))}
                    <span className="text-gray-500 ml-1">({booking.hotels?.stars} étoiles)</span>
                  </div>
                </div>
              </div>

              {/* Stay Details */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Calendar className="w-5 h-5" />
                  Détails du séjour
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-sm text-gray-600">Arrivée</p>
                    <p className="font-semibold">{formatDate(booking.check_in_date)}</p>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-sm text-gray-600">Départ</p>
                    <p className="font-semibold">{formatDate(booking.check_out_date)}</p>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-sm text-gray-600">Durée</p>
                    <p className="font-semibold">{calculateNights()} nuit(s)</p>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-sm text-gray-600">Chambres</p>
                    <p className="font-semibold">{booking.rooms}</p>
                  </div>
                </div>
              </div>

              {/* Guest Information */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  Informations voyageurs
                </h3>
                <div className="bg-gray-50 p-6 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Nom du client</p>
                      <p className="font-semibold">
                        {booking.customers?.first_name} {booking.customers?.last_name}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Email</p>
                      <p className="font-semibold">{booking.customers?.email}</p>
                    </div>
                    {booking.customers?.phone && (
                      <div>
                        <p className="text-sm text-gray-600">Téléphone</p>
                        <p className="font-semibold">{booking.customers?.phone}</p>
                      </div>
                    )}
                    <div>
                      <p className="text-sm text-gray-600">Voyageurs</p>
                      <p className="font-semibold">
                        {booking.adults} adulte(s)
                        {booking.children > 0 && `, ${booking.children} enfant(s)`}
                      </p>
                    </div>
                  </div>
                  
                  {booking.special_requests && (
                    <div className="mt-4">
                      <p className="text-sm text-gray-600">Demandes spéciales</p>
                      <p className="font-semibold">{booking.special_requests}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Payment Information */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <CreditCard className="w-5 h-5" />
                  Informations de paiement
                </h3>
                <div className="bg-gray-50 p-6 rounded-lg">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm text-gray-600">Montant total</p>
                      <p className="text-2xl font-bold text-primary">
                        {booking.total_amount?.toLocaleString()} {booking.currency}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600">Statut du paiement</p>
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                        booking.payment_status === 'paid' 
                          ? 'bg-green-100 text-green-800'
                          : booking.payment_status === 'pending'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {booking.payment_status === 'paid' ? 'Payé' : 
                         booking.payment_status === 'pending' ? 'En attente' :
                         booking.payment_status === 'failed' ? 'Échoué' :
                         booking.payment_status === 'refunded' ? 'Remboursé' : booking.payment_status}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              <div className="bg-blue-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-blue-900 mb-2">
                  Besoin d'aide ?
                </h3>
                <p className="text-blue-800">
                  Pour toute question concernant votre réservation, contactez-nous :
                </p>
                <div className="mt-2 text-blue-800">
                  <p>📧 Email: <EMAIL></p>
                  <p>📞 Téléphone: +213 123 456 789</p>
                  <p className="text-sm mt-2">
                    Mentionnez votre numéro de réservation: <strong>{booking.booking_reference}</strong>
                  </p>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
