import { useState } from 'react';
import { Upload, Link, X, Image as ImageIcon } from 'lucide-react';
import { supabase } from '@/Config/supabase';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';

export default function ImageUpload({ 
  value = [], 
  onChange, 
  maxImages = 5, 
  bucket = 'hotel-images',
  folder = 'hotels' 
}) {
  const [uploading, setUploading] = useState(false);
  const [urlInput, setUrlInput] = useState('');
  const [uploadMethod, setUploadMethod] = useState('file'); // 'file' or 'url'

  const uploadFile = async (file) => {
    try {
      setUploading(true);
      
      // Generate unique filename
      const fileExt = file.name.split('.').pop();
      const fileName = `${folder}/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      
      // Upload to Supabase Storage
      const { data, error } = await supabase.storage
        .from(bucket)
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) throw error;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from(bucket)
        .getPublicUrl(fileName);

      return publicUrl;
    } catch (error) {
      console.error('Upload error:', error);
      throw error;
    } finally {
      setUploading(false);
    }
  };

  const handleFileUpload = async (e) => {
    const files = Array.from(e.target.files);
    
    if (value.length + files.length > maxImages) {
      alert(`Maximum ${maxImages} images allowed`);
      return;
    }

    try {
      const uploadPromises = files.map(uploadFile);
      const uploadedUrls = await Promise.all(uploadPromises);
      onChange([...value, ...uploadedUrls]);
    } catch (error) {
      alert('Upload failed: ' + error.message);
    }
  };

  const handleUrlAdd = () => {
    if (!urlInput.trim()) return;
    
    if (value.length >= maxImages) {
      alert(`Maximum ${maxImages} images allowed`);
      return;
    }

    onChange([...value, urlInput.trim()]);
    setUrlInput('');
  };

  const removeImage = (index) => {
    const newImages = value.filter((_, i) => i !== index);
    onChange(newImages);
  };

  return (
    <div className="space-y-4">
      {/* Upload Method Toggle */}
      <div className="flex gap-2 mb-4">
        <Button
          type="button"
          variant={uploadMethod === 'file' ? 'primary' : 'outline'}
          size="sm"
          onClick={() => setUploadMethod('file')}
        >
          <Upload className="w-4 h-4 mr-2" />
          Upload Files
        </Button>
        <Button
          type="button"
          variant={uploadMethod === 'url' ? 'primary' : 'outline'}
          size="sm"
          onClick={() => setUploadMethod('url')}
        >
          <Link className="w-4 h-4 mr-2" />
          Add URL
        </Button>
      </div>

      {/* File Upload */}
      {uploadMethod === 'file' && (
        <div>
          <input
            type="file"
            multiple
            accept="image/*"
            onChange={handleFileUpload}
            disabled={uploading || value.length >= maxImages}
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-primary/90"
          />
          {uploading && <p className="text-sm text-gray-500 mt-2">Uploading...</p>}
        </div>
      )}

      {/* URL Input */}
      {uploadMethod === 'url' && (
        <div className="flex gap-2">
          <Input
            type="url"
            placeholder="https://example.com/image.jpg"
            value={urlInput}
            onChange={(e) => setUrlInput(e.target.value)}
            className="flex-1"
          />
          <Button
            type="button"
            onClick={handleUrlAdd}
            disabled={!urlInput.trim() || value.length >= maxImages}
          >
            Add
          </Button>
        </div>
      )}

      {/* Image Preview Grid */}
      {value.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {value.map((imageUrl, index) => (
            <div key={index} className="relative group">
              <img
                src={imageUrl}
                alt={`Image ${index + 1}`}
                className="w-full h-32 object-cover rounded-lg border"
              />
              <button
                type="button"
                onClick={() => removeImage(index)}
                className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          ))}
        </div>
      )}

      <p className="text-sm text-gray-500">
        {value.length}/{maxImages} images uploaded
      </p>
    </div>
  );
}
