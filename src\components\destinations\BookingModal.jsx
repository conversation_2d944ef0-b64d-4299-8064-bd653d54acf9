"use client";
import { useState } from "react";
import { X } from "lucide-react";
import Button from "@/components/ui/Button";
import GuestBookingForm from "@/components/booking/GuestBookingForm";

export default function BookingModal({ hotel, isOpen, onClose, onBookingComplete }) {
  const [bookingStep, setBookingStep] = useState('form'); // 'form' or 'success'
  const [bookingData, setBookingData] = useState(null);

  const handleBookingComplete = (data) => {
    setBookingData(data);
    setBookingStep('success');
    if (onBookingComplete) {
      onBookingComplete(data);
    }
  };

  const handleClose = () => {
    setBookingStep('form');
    setBookingData(null);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 animate-fade-in">
      <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto relative animate-scale-in">
        {/* Close Button */}
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 z-10 p-2 hover:bg-gray-100 rounded-full transition-all duration-200 hover:scale-110"
          aria-label="Fermer"
        >
          <X className="w-6 h-6" />
        </button>

        {/* Content */}
        {bookingStep === 'form' ? (
          <GuestBookingForm
            hotel={hotel}
            onBookingComplete={handleBookingComplete}
            onCancel={handleClose}
          />
        ) : (
          <BookingSuccess
            bookingData={bookingData}
            onClose={handleClose}
          />
        )}
      </div>
    </div>
  );
}

function BookingSuccess({ bookingData, onClose }) {
  const booking = bookingData?.booking;
  const customer = bookingData?.customer;

  return (
    <div className="p-8 text-center">
      <div className="mb-6">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <span className="text-green-500 text-2xl">✓</span>
        </div>
        <h2 className="text-3xl font-bold text-gray-900 mb-2">
          Réservation Confirmée !
        </h2>
        <p className="text-gray-600">
          Votre réservation a été enregistrée avec succès
        </p>
      </div>

      {booking && (
        <div className="bg-gray-50 rounded-lg p-6 mb-6 text-left max-w-md mx-auto">
          <h3 className="font-semibold text-gray-900 mb-4">Détails de la réservation</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Référence :</span>
              <span className="font-medium">{booking.booking_reference}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Hôtel :</span>
              <span className="font-medium">{booking.hotels?.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Client :</span>
              <span className="font-medium">
                {customer?.first_name} {customer?.last_name}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Email :</span>
              <span className="font-medium">{customer?.email}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Arrivée :</span>
              <span className="font-medium">
                {new Date(booking.check_in_date).toLocaleDateString()}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Départ :</span>
              <span className="font-medium">
                {new Date(booking.check_out_date).toLocaleDateString()}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Total :</span>
              <span className="font-bold text-primary">
                {booking.total_amount?.toLocaleString()} {booking.currency}
              </span>
            </div>
          </div>
        </div>
      )}

      <div className="space-y-4">
        <p className="text-sm text-gray-600">
          Un email de confirmation a été envoyé à votre adresse email.
          Vous pouvez également retrouver votre réservation avec la référence ci-dessus.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button
            variant="primary"
            onClick={onClose}
          >
            Continuer à explorer
          </Button>
          <Button
            variant="outline"
            onClick={() => window.location.href = '/'}
          >
            Retour à l'accueil
          </Button>
        </div>
      </div>
    </div>
  );
}
