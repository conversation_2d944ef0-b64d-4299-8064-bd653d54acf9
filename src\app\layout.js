import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { Poppins } from "next/font/google";
import "./globals.css";
const poppins = Poppins({
  weight: ["400", "500", "600", "700"],
  subsets: ["latin"],
  variable: "--font-poppins",
});

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: "Bouguerrouche Travel - Voyages en Tunisie",
  description:
    "Votre partenaire de confiance pour des voyages inoubliables en Tunisie. Découvrez nos offres exclusives et réservez votre séjour.",
  keywords:
    "voyage Tunisie, agence voyage Alger, séjour Tunisie, hôtel Tunisie, vacances",
  openGraph: {
    title: "Bouguerrouche Travel - Voyages en Tunisie",
    description:
      "Votre partenaire de confiance pour des voyages inoubliables en Tunisie",
    type: "website",
    locale: "fr_DZ",
  },
};

import ErrorBoundary from '@/components/ErrorBoundary';
import { ToastProvider } from '@/components/ui/Toast';

export default function RootLayout({ children }) {
  return (
    <html lang="fr">
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${poppins.variable} antialiased`}
      >
        <ErrorBoundary>
          <ToastProvider>
            {children}
          </ToastProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}
