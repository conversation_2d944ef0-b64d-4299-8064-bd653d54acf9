-- Create categories table
CREATE TABLE service_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL
);

-- Create services table
CREATE TABLE services (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category_id INT,
    name VARCHAR(255) NOT NULL,
    FOREIGN KEY (category_id) REFERENCES service_categories(id)
);

-- Insert categories
INSERT INTO service_categories (name) VALUES
('Équipements des chambres'),
('Restauration et boissons'),
('Bien-être et spa'),
('Loisirs et activités'),
('Services pour les familles'),
('Transport et stationnement'),
('Affaires et événements'),
('Services généraux'),
('Nettoyage et blanchisserie'),
('Accessibilité'),
('Animaux domestiques');

-- Insert services
INSERT INTO services (category_id, name) VALUES
-- Équipements des chambres
(1, 'Wi-Fi gratuit'),
(1, 'Climatisation'),
(1, 'Chauffage'),
(1, 'Télévision à écran plat'),
(1, 'Chaînes câblées / satellite'),
(1, 'Minibar'),
(1, 'Coffre-fort'),
(1, 'Bureau'),
(1, 'Bouilloire électrique'),
(1, 'Machine à café / thé'),
(1, 'Fer et planche à repasser'),
(1, 'Service en chambre'),
(1, 'Service de ménage quotidien'),
(1, 'Salle de bain privée'),
(1, 'Sèche-cheveux'),
(1, 'Peignoir et chaussons'),
(1, 'Serviettes et articles de toilette'),
(1, 'Balcon ou terrasse'),

-- Restauration et boissons
(2, 'Restaurant(s)'),
(2, 'Bar / lounge'),
(2, 'Petit-déjeuner buffet'),
(2, 'Service en chambre (repas)'),
(2, 'Café / snack-bar'),
(2, 'Bar au bord de la piscine'),
(2, 'Menus spéciaux (sur demande)'),
(2, 'Repas pour enfants'),

-- Bien-être et spa
(3, 'Centre de bien-être'),
(3, 'Sauna'),
(3, 'Hammam (bain turc)'),
(3, 'Jacuzzi / bain à remous'),
(3, 'Massages'),
(3, 'Salon de beauté'),
(3, 'Salle de sport'),
(3, 'Cours de yoga'),

-- Loisirs et activités
(4, 'Piscine extérieure'),
(4, 'Piscine intérieure'),
(4, 'Plage privée'),
(4, 'Chaises longues et parasols'),
(4, 'Sports nautiques'),
(4, 'Plongée / snorkeling'),
(4, 'Court de tennis'),
(4, 'Golf à proximité'),
(4, 'Salle de jeux'),
(4, 'Équipe d’animation'),
(4, 'Discothèque / DJ'),

-- Services pour les familles
(5, 'Club enfants'),
(5, 'Service de garde d’enfants'),
(5, 'Piscine pour enfants'),
(5, 'Aire de jeux'),
(5, 'Chambres familiales'),
(5, 'Jeux de société / puzzles'),

-- Transport et stationnement
(6, 'Parking gratuit'),
(6, 'Service de voiturier'),
(6, 'Navette aéroport'),
(6, 'Location de voitures'),
(6, 'Location de vélos'),
(6, 'Service de taxi'),

-- Affaires et événements
(7, 'Salles de réunion'),
(7, 'Centre d’affaires'),
(7, 'Salle de conférence'),
(7, 'Fax / photocopie'),
(7, 'Internet haut débit'),

-- Services généraux
(8, 'Réception ouverte 24h/24'),
(8, 'Service de conciergerie'),
(8, 'Bureau d’excursions'),
(8, 'Change de devises'),
(8, 'Enregistrement/départ express'),
(8, 'Consigne à bagages'),
(8, 'Personnel multilingue'),
(8, 'Ascenseur'),
(8, 'Service de réveil'),

-- Nettoyage et blanchisserie
(9, 'Service de blanchisserie'),
(9, 'Nettoyage à sec'),
(9, 'Service de repassage'),
(9, 'Cirage de chaussures'),

-- Accessibilité
(10, 'Accès fauteuil roulant'),
(10, 'Chambres accessibles'),
(10, 'Ascenseur adapté'),
(10, 'Barres d’appui dans la salle de bain'),

-- Animaux domestiques
(11, 'Animaux acceptés (sur demande)'),
(11, 'Gamelles pour animaux'),
(11, 'Panier pour animaux');
