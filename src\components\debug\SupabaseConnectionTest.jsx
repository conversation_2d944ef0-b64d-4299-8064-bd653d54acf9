"use client";
import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import supabase from "@/Config/supabase";
import { validateEnvironment, debugEnvironment } from "@/lib/env-check";

export default function SupabaseConnectionTest() {
  const [connectionStatus, setConnectionStatus] = useState('testing');
  const [testResults, setTestResults] = useState({});
  const [error, setError] = useState(null);

  useEffect(() => {
    runConnectionTests();
  }, []);

  const runConnectionTests = async () => {
    setConnectionStatus('testing');
    setError(null);
    
    const results = {
      environment: null,
      connection: null,
      authentication: null,
      database: null
    };

    try {
      // Test 1: Environment Variables
      console.log('🔍 Testing environment variables...');
      debugEnvironment();
      const envCheck = validateEnvironment();
      results.environment = {
        status: envCheck.isValid ? 'success' : 'error',
        details: envCheck.isValid ? 'All environment variables are set' : `Missing: ${envCheck.missing.join(', ')}`,
        data: envCheck
      };

      // Test 2: Supabase Connection
      console.log('🔗 Testing Supabase connection...');
      const { data: connectionData, error: connectionError } = await supabase
        .from('countries')
        .select('count')
        .limit(1);
      
      results.connection = {
        status: connectionError ? 'error' : 'success',
        details: connectionError ? connectionError.message : 'Connection successful',
        data: connectionData
      };

      // Test 3: Authentication
      console.log('🔐 Testing authentication...');
      const { data: authData, error: authError } = await supabase.auth.getSession();
      results.authentication = {
        status: 'success', // Auth is optional for anon access
        details: authData?.session ? 'User authenticated' : 'Anonymous access (expected for guest booking)',
        data: authData
      };

      // Test 4: Database Operations
      console.log('📊 Testing database operations...');
      const { data: dbData, error: dbError } = await supabase
        .from('countries')
        .select('id, name, is_featured')
        .limit(3);
      
      results.database = {
        status: dbError ? 'error' : 'success',
        details: dbError ? dbError.message : `Retrieved ${dbData?.length || 0} countries`,
        data: dbData
      };

      setTestResults(results);
      
      // Overall status
      const hasErrors = Object.values(results).some(result => result.status === 'error');
      setConnectionStatus(hasErrors ? 'error' : 'success');

    } catch (error) {
      console.error('Connection test failed:', error);
      setError(error.message);
      setConnectionStatus('error');
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success':
        return '✅';
      case 'error':
        return '❌';
      case 'testing':
        return '🔄';
      default:
        return '⚪';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success':
        return 'text-green-600 bg-green-50';
      case 'error':
        return 'text-red-600 bg-red-50';
      case 'testing':
        return 'text-blue-600 bg-blue-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <Card>
        <CardContent className="p-8">
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Supabase Connection Test
            </h1>
            <p className="text-gray-600">
              Diagnostic tool to verify Supabase configuration and connectivity
            </p>
          </div>

          {/* Overall Status */}
          <div className={`p-4 rounded-lg mb-6 ${getStatusColor(connectionStatus)}`}>
            <div className="flex items-center gap-3">
              <span className="text-2xl">{getStatusIcon(connectionStatus)}</span>
              <div>
                <h2 className="text-lg font-semibold">
                  Overall Status: {connectionStatus === 'testing' ? 'Testing...' : 
                                 connectionStatus === 'success' ? 'All Systems Operational' : 
                                 'Issues Detected'}
                </h2>
                {error && <p className="text-sm mt-1">{error}</p>}
              </div>
            </div>
          </div>

          {/* Test Results */}
          <div className="space-y-4">
            {Object.entries(testResults).map(([testName, result]) => (
              <div key={testName} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-lg font-semibold capitalize flex items-center gap-2">
                    {getStatusIcon(result.status)}
                    {testName.replace(/([A-Z])/g, ' $1').trim()}
                  </h3>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(result.status)}`}>
                    {result.status}
                  </span>
                </div>
                <p className="text-gray-600 mb-2">{result.details}</p>
                
                {/* Show additional data for debugging */}
                {result.data && (
                  <details className="mt-2">
                    <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
                      Show technical details
                    </summary>
                    <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-4 mt-6">
            <Button
              variant="primary"
              onClick={runConnectionTests}
              disabled={connectionStatus === 'testing'}
            >
              {connectionStatus === 'testing' ? 'Testing...' : 'Run Tests Again'}
            </Button>
            
            {connectionStatus === 'success' && (
              <Button
                variant="outline"
                onClick={() => window.location.href = '/admin/destinations'}
              >
                Go to Admin Dashboard
              </Button>
            )}
          </div>

          {/* Troubleshooting Guide */}
          {connectionStatus === 'error' && (
            <div className="mt-8 p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="text-lg font-semibold text-yellow-800 mb-3">
                🛠️ Troubleshooting Guide
              </h3>
              <div className="space-y-2 text-yellow-700">
                <p><strong>1. Environment Variables:</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>Ensure NEXT_PUBLIC_SUPABASE_URL is set correctly</li>
                  <li>Ensure NEXT_PUBLIC_SUPABASE_ANON_KEY is set with the anon key (not service role)</li>
                  <li>Restart your development server after changing .env</li>
                </ul>
                
                <p><strong>2. Supabase Configuration:</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>Verify your Supabase project URL is correct</li>
                  <li>Check that Row Level Security policies allow anon access</li>
                  <li>Ensure your database tables exist</li>
                </ul>
                
                <p><strong>3. Network Issues:</strong></p>
                <ul className="list-disc list-inside ml-4 space-y-1">
                  <li>Check your internet connection</li>
                  <li>Verify Supabase service status</li>
                  <li>Try accessing your Supabase dashboard directly</li>
                </ul>
              </div>
            </div>
          )}

          {/* Environment Info */}
          <div className="mt-8 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-sm font-semibold text-gray-700 mb-2">Environment Information</h3>
            <div className="text-xs text-gray-600 space-y-1">
              <p>Supabase URL: {process.env.NEXT_PUBLIC_SUPABASE_URL || 'Not set'}</p>
              <p>Anon Key: {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set ✅' : 'Not set ❌'}</p>
              <p>Environment: {process.env.NODE_ENV}</p>
              <p>Timestamp: {new Date().toISOString()}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
