import { NextResponse } from 'next/server';

export async function GET() {
  try {
    const config = {
      cloudName: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
      hasApiKey: !!process.env.CLOUDINARY_API_KEY,
      hasApiSecret: !!process.env.CLOUDINARY_API_SECRET,
      hasCloudinaryUrl: !!process.env.CLOUDINARY_URL,
      isConfigured: !!(
        process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME &&
        process.env.CLOUDINARY_API_KEY &&
        process.env.CLOUDINARY_API_SECRET
      )
    };

    return NextResponse.json({
      success: true,
      config: config
    });
  } catch (error) {
    console.error('Cloudinary config API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Erreur lors de la vérification de la configuration',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
