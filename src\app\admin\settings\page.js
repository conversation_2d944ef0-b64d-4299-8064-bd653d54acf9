"use client";
import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import { Save, Globe, Mail, Bell, Shield, CheckCircle, AlertCircle, RefreshCw } from "lucide-react";
import { getSettings, updateSetting } from "@/lib/database";

export default function Settings() {
  const [settings, setSettings] = useState({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [activeTab, setActiveTab] = useState("general");

  // Default settings structure
  const defaultSettings = {
    site_name: "Bouguerrouche Travel",
    site_description: "Votre partenaire de voyage de confiance en Algérie",
    contact_email: "<EMAIL>",
    contact_phone: "+213 123 456 789",
    contact_address: "Alger, Algérie",
    business_hours: "Lun-Ven: 9h-18h, Sam: 9h-13h",
    currency_default: "DA",
    language_default: "fr",
    booking_confirmation_email: "true",
    newsletter_enabled: "true",
    maintenance_mode: "false",
    max_booking_days_advance: "365",
    min_booking_days_advance: "1",
    cancellation_policy: "Annulation gratuite jusqu'à 24h avant l'arrivée",
    terms_of_service: "Conditions générales de service...",
    privacy_policy: "Politique de confidentialité...",
    social_facebook: "",
    social_instagram: "",
    social_twitter: "",
    social_linkedin: "",
    analytics_google_id: "",
    email_smtp_host: "",
    email_smtp_port: "587",
    email_smtp_username: "",
    email_smtp_password: "",
    payment_stripe_public_key: "",
    payment_stripe_secret_key: "",
    seo_meta_title: "Bouguerrouche Travel - Agence de Voyage",
    seo_meta_description: "Découvrez nos destinations exceptionnelles avec Bouguerrouche Travel",
    seo_meta_keywords: "voyage, tunisie, algérie, réservation, hôtel"
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    setLoading(true);
    try {
      const { data, error } = await getSettings();
      if (error) {
        setError(`Erreur lors du chargement des paramètres: ${error.message}`);
      } else {
        // Convert array of settings to object
        const settingsObj = {};
        if (data && Array.isArray(data)) {
          data.forEach(setting => {
            settingsObj[setting.key] = setting.value;
          });
        }

        // Merge with defaults for any missing settings
        const mergedSettings = { ...defaultSettings, ...settingsObj };
        setSettings(mergedSettings);
      }
    } catch (error) {
      setError(`Erreur de connexion: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    setError(null);
    setSuccess(false);

    try {
      // Save each setting individually
      const savePromises = Object.entries(settings).map(([key, value]) =>
        updateSetting(key, value)
      );

      const results = await Promise.all(savePromises);
      const hasErrors = results.some(result => result.error);

      if (hasErrors) {
        setError("Certains paramètres n'ont pas pu être sauvegardés");
      } else {
        setSuccess(true);
        setTimeout(() => setSuccess(false), 3000);
      }
    } catch (error) {
      setError(`Erreur lors de la sauvegarde: ${error.message}`);
    } finally {
      setSaving(false);
    }
  };

  const updateSetting = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-lg text-gray-600">Chargement des paramètres...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container-custom">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Paramètres</h1>
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              icon={<RefreshCw className="w-4 h-4" />}
              onClick={fetchSettings}
              disabled={loading}
            >
              Actualiser
            </Button>
            <Button
              variant="primary"
              loading={saving}
              icon={<Save className="w-4 h-4" />}
              onClick={handleSave}
            >
              Sauvegarder
            </Button>
          </div>
        </div>

        {/* Status Messages */}
        {error && (
          <Card className="mb-6 border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-3 text-red-700">
                <AlertCircle className="w-5 h-5" />
                <span>{error}</span>
              </div>
            </CardContent>
          </Card>
        )}

        {success && (
          <Card className="mb-6 border-green-200 bg-green-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-3 text-green-700">
                <CheckCircle className="w-5 h-5" />
                <span>Paramètres sauvegardés avec succès!</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Tabs */}
        <div className="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg overflow-x-auto">
          {[
            { id: 'general', label: 'Général', icon: Globe },
            { id: 'contact', label: 'Contact', icon: Mail },
            { id: 'booking', label: 'Réservations', icon: Bell },
            { id: 'social', label: 'Réseaux Sociaux', icon: Shield },
            { id: 'technical', label: 'Technique', icon: Shield }
          ].map(tab => (
            <button
              key={tab.id}
              className={`flex items-center gap-2 px-4 py-2 rounded-md font-medium whitespace-nowrap transition-colors ${
                activeTab === tab.id
                  ? "bg-white text-primary shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
              onClick={() => setActiveTab(tab.id)}
            >
              <tab.icon className="w-4 h-4" />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Settings Content */}
        <Card>
          <CardContent className="p-6">
            {/* General Settings */}
            {activeTab === "general" && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900">Paramètres Généraux</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Nom du site
                    </label>
                    <Input
                      type="text"
                      value={settings.site_name || ''}
                      onChange={(e) => updateSetting('site_name', e.target.value)}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Devise par défaut
                    </label>
                    <select
                      value={settings.currency_default || 'DA'}
                      onChange={(e) => updateSetting('currency_default', e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-lg"
                    >
                      <option value="DA">Dinar Algérien (DA)</option>
                      <option value="EUR">Euro (EUR)</option>
                      <option value="USD">Dollar US (USD)</option>
                      <option value="TND">Dinar Tunisien (TND)</option>
                    </select>
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Description du site
                    </label>
                    <textarea
                      className="w-full p-3 border border-gray-300 rounded-lg"
                      rows={3}
                      value={settings.site_description || ''}
                      onChange={(e) => updateSetting('site_description', e.target.value)}
                      placeholder="Description de votre agence de voyage..."
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Contact Settings */}
            {activeTab === "contact" && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900">Informations de Contact</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email de contact
                    </label>
                    <Input
                      type="email"
                      value={settings.contact_email || ''}
                      onChange={(e) => updateSetting('contact_email', e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Numéro de téléphone
                    </label>
                    <Input
                      type="tel"
                      value={settings.contact_phone || ''}
                      onChange={(e) => updateSetting('contact_phone', e.target.value)}
                      placeholder="+213 123 456 789"
                    />
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Adresse
                    </label>
                    <Input
                      type="text"
                      value={settings.contact_address || ''}
                      onChange={(e) => updateSetting('contact_address', e.target.value)}
                      placeholder="Adresse complète de l'agence"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Booking Settings */}
            {activeTab === "booking" && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900">Paramètres de Réservation</h2>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium text-gray-900">
                        Email de confirmation automatique
                      </h3>
                      <p className="text-sm text-gray-500">
                        Envoyer automatiquement un email de confirmation
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      checked={settings.booking_confirmation_email === 'true'}
                      onChange={(e) => updateSetting('booking_confirmation_email', e.target.checked ? 'true' : 'false')}
                      className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium text-gray-900">
                        Newsletter activée
                      </h3>
                      <p className="text-sm text-gray-500">
                        Permettre l&apos;inscription à la newsletter
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      checked={settings.newsletter_enabled === 'true'}
                      onChange={(e) => updateSetting('newsletter_enabled', e.target.checked ? 'true' : 'false')}
                      className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Social Media Settings */}
            {activeTab === "social" && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900">Réseaux Sociaux</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Facebook
                    </label>
                    <Input
                      type="url"
                      value={settings.social_facebook || ''}
                      onChange={(e) => updateSetting('social_facebook', e.target.value)}
                      placeholder="https://facebook.com/votrepage"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Instagram
                    </label>
                    <Input
                      type="url"
                      value={settings.social_instagram || ''}
                      onChange={(e) => updateSetting('social_instagram', e.target.value)}
                      placeholder="https://instagram.com/votrepage"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Technical Settings */}
            {activeTab === "technical" && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900">Paramètres Techniques</h2>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Google Analytics ID
                    </label>
                    <Input
                      type="text"
                      value={settings.analytics_google_id || ''}
                      onChange={(e) => updateSetting('analytics_google_id', e.target.value)}
                      placeholder="G-XXXXXXXXXX"
                    />
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

      </div>
    </div>
  );
}
