/**
 * Client-side Cloudinary utilities
 * Safe to use in browser environment
 */

/**
 * Get Cloudinary configuration status (Client-safe)
 * @returns {Object} Configuration status
 */
export const getCloudinaryConfig = () => {
  const cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME;

  return {
    cloudName: cloudName,
    hasApiKey: true, // We assume API key is configured server-side
    hasApiSecret: true, // We assume API secret is configured server-side
    hasCloudinaryUrl: true, // We assume CLOUDINARY_URL is configured server-side
    isConfigured: !!cloudName // Only check what we can verify client-side
  };
};

/**
 * Get Cloudinary configuration status from server (Client-safe)
 * @returns {Promise<Object>} Configuration status
 */
export const getCloudinaryConfigFromServer = async () => {
  try {
    const response = await fetch('/api/cloudinary/config');
    const result = await response.json();

    if (result.success) {
      return result.config;
    } else {
      throw new Error(result.error || 'Failed to get configuration');
    }
  } catch (error) {
    console.error('Error getting Cloudinary config:', error);
    // Fallback to client-side check
    return getCloudinaryConfig();
  }
};

/**
 * Generate optimized Cloudinary URL (Client-safe)
 * @param {string} publicId - Cloudinary public ID
 * @param {Object} options - Transformation options
 * @returns {string} Optimized URL
 */
export const getOptimizedImageUrl = (publicId, options = {}) => {
  if (!publicId) return '/images/placeholder.jpg';
  
  const cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME;
  if (!cloudName) return '/images/placeholder.jpg';
  
  // Build transformation string
  const transformations = [];
  
  // Quality optimization
  transformations.push(`q_${options.quality || 'auto:good'}`);
  
  // Format optimization
  transformations.push('f_auto');
  
  // Responsive sizing
  if (options.width || options.height) {
    const crop = options.crop || 'fill';
    const gravity = options.gravity || 'auto';
    
    if (options.width) transformations.push(`w_${options.width}`);
    if (options.height) transformations.push(`h_${options.height}`);
    transformations.push(`c_${crop}`);
    if (gravity !== 'auto') transformations.push(`g_${gravity}`);
  }
  
  // Additional transformations
  if (options.blur) transformations.push(`e_blur:${options.blur}`);
  if (options.grayscale) transformations.push('e_grayscale');

  const transformationString = transformations.join(',');
  
  return `https://res.cloudinary.com/${cloudName}/image/upload/${transformationString}/${publicId}`;
};

/**
 * Generate responsive image URLs for different screen sizes (Client-safe)
 * @param {string} publicId - Cloudinary public ID
 * @returns {Object} Object with different sized URLs
 */
export const getResponsiveImageUrls = (publicId) => {
  if (!publicId) {
    const placeholder = '/images/placeholder.jpg';
    return {
      thumbnail: placeholder,
      small: placeholder,
      medium: placeholder,
      large: placeholder,
      original: placeholder
    };
  }

  return {
    thumbnail: getOptimizedImageUrl(publicId, { width: 150, height: 150 }),
    small: getOptimizedImageUrl(publicId, { width: 400, height: 300 }),
    medium: getOptimizedImageUrl(publicId, { width: 800, height: 600 }),
    large: getOptimizedImageUrl(publicId, { width: 1200, height: 900 }),
    original: getOptimizedImageUrl(publicId, { width: 1920, height: 1440 })
  };
};

/**
 * Validate image file before upload (Client-safe)
 * @param {File} file - File to validate
 * @returns {Object} Validation result
 */
export const validateImageFile = (file) => {
  const maxSize = 10 * 1024 * 1024; // 10MB
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  
  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'Type de fichier non supporté. Utilisez JPG, PNG ou WebP.'
    };
  }
  
  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'Fichier trop volumineux. Maximum 10MB.'
    };
  }
  
  return { valid: true };
};

/**
 * Upload image via API route (Client-safe)
 * @param {File} file - File to upload
 * @param {Object} options - Upload options
 * @returns {Promise<Object>} Upload result
 */
export const uploadImageToCloudinary = async (file, options = {}) => {
  try {
    // Validate file first
    const validation = validateImageFile(file);
    if (!validation.valid) {
      return {
        success: false,
        error: validation.error
      };
    }

    const formData = new FormData();
    formData.append('file', file);
    formData.append('folder', options.folder || 'hotels');
    if (options.tags) {
      formData.append('tags', options.tags.join(','));
    }

    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData
    });

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Upload error:', error);
    return {
      success: false,
      error: 'Erreur lors de l\'upload'
    };
  }
};

/**
 * Test Cloudinary connection via API route (Client-safe)
 * @returns {Promise<Object>} Connection test result
 */
export const testCloudinaryConnection = async () => {
  try {
    const response = await fetch('/api/cloudinary/test', {
      method: 'POST'
    });

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Connection test error:', error);
    return {
      success: false,
      error: 'Erreur lors du test de connexion'
    };
  }
};

/**
 * Delete image via API route (Client-safe)
 * @param {string} publicId - Cloudinary public ID
 * @returns {Promise<Object>} Delete result
 */
export const deleteImageFromCloudinary = async (publicId) => {
  try {
    const response = await fetch(`/api/upload?publicId=${encodeURIComponent(publicId)}`, {
      method: 'DELETE'
    });

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Delete error:', error);
    return {
      success: false,
      error: 'Erreur lors de la suppression'
    };
  }
};
